# 🚀 Simple Token Manager - 精简版无感换号

## 📋 项目概述

基于success case的核心逻辑，创建的精简版无感换号系统。通过劫持VSCode插件的activate函数，实现token的无感注入和管理。

## 🎯 核心特性

- ✅ **VSCode Secrets劫持**: 使用相同键名覆盖原插件的token存储
- ✅ **简化认证**: 使用简单的Bearer Token认证，无需OAuth
- ✅ **最小侵入**: 只在extension.js末尾添加少量代码
- ✅ **错误隔离**: 确保不影响原插件正常运行
- ✅ **一键更新**: 支持快速token切换
- ✅ **可视化界面**: 提供简洁的侧边栏管理界面

## 📁 文件结构

```
injection_file/
├── token-manager.js          # 核心token管理器
├── custom-features.js        # UI界面和用户交互
├── injection-guide.md        # 劫持安装指导
├── usage-guide.md           # 使用说明文档
├── package-contribution.json # VSCode扩展配置
└── README.md               # 项目说明（本文件）
```

## 🔧 快速开始

### 1. 配置后端API

修改 `token-manager.js` 顶部的配置：

```javascript
static API_BASE_URL = 'http://your-backend.com';     // 你的后端地址
static AUTH_PASSWORD = 'your_secret_password';       // 认证密码
```

### 2. 准备后端API

你的后端需要提供一个端点：

```http
GET /api/tokens
Authorization: Bearer your_secret_password
```

返回格式：
```json
{
  "tokens": [
    {
      "id": "token_1",
      "accessToken": "实际的JWT token",
      "tenantURL": "https://d5.api.augmentcode.com/"
    }
  ]
}
```

### 3. 安装到VSCode

1. 找到Augment插件目录：
   ```
   ~/.vscode/extensions/augmentcode.augment-*/out/
   ```

2. 复制文件：
   ```bash
   cp token-manager.js /path/to/augment/out/
   cp custom-features.js /path/to/augment/out/
   ```

3. 修改extension.js（详见 `injection-guide.md`）

4. 重启VSCode

## 🎛️ 使用方法

### 侧边栏界面
- 在VSCode侧边栏找到 "Simple Token Manager"
- 点击 "🚀 一键更新Token" 快速切换
- 点击 "📋 查看当前Token" 查看状态

### 命令面板
- `Ctrl+Shift+P` → "Simple Token: Quick Update"
- `Ctrl+Shift+P` → "Simple Token: Open Manager"

## 🔍 技术原理

### 核心劫持机制

```javascript
// 1. 保存原始exports
const originalExports = module.exports ? { ...module.exports } : {};

// 2. 劫持activate函数
async function customActivate(context) {
    // 先调用原始activate
    await originalExports.activate(context);
    
    // 延迟初始化我们的功能
    setTimeout(async () => {
        customFeatures = new CustomFeatures();
        await customFeatures.initialize(context);
    }, 1000);
}

// 3. 覆盖module.exports
module.exports = {
    ...originalExports,
    activate: customActivate
};
```

### Token注入原理

```javascript
// 使用相同键名劫持原插件的存储
await context.secrets.store('augment.sessions', JSON.stringify({
    accessToken: "新的token",
    tenantURL: "https://d5.api.augmentcode.com/",
    scopes: ["simple_token_manager_v1"]
}));
```

## 🛡️ 安全考虑

1. **认证简化**: 使用Bearer Token而非OAuth，降低复杂度
2. **错误隔离**: 大量try-catch确保不影响原插件
3. **权限最小**: 只劫持必要的功能
4. **可回滚**: 保留原文件备份，支持快速回滚

## 📊 与Success Case对比

| 特性 | Success Case | Simple Token Manager |
|------|-------------|---------------------|
| 认证方式 | 复杂OAuth + API Key | 简单Bearer Token |
| 代码复杂度 | 高（混淆） | 低（可读） |
| 配置难度 | 复杂 | 简单 |
| 功能完整性 | 完整 | 精简核心功能 |
| 维护性 | 困难 | 容易 |

## ⚠️ 注意事项

1. **插件更新**: Augment插件更新会覆盖修改，需重新注入
2. **备份重要**: 始终保留extension.js备份
3. **测试环境**: 建议先在测试环境验证
4. **兼容性**: 确保与最新版Augment插件兼容

## 🐛 故障排除

### 常见问题

1. **看不到侧边栏**: 检查文件路径和权限
2. **无法获取token**: 检查后端API配置
3. **注入失败**: 检查token格式和权限
4. **原插件异常**: 检查劫持代码语法

### 调试方法

1. 打开VSCode开发者工具 (`Ctrl+Shift+I`)
2. 查看Console标签的日志输出
3. 搜索 `[TokenManager]` 和 `[CustomFeatures]` 前缀

## 📚 文档索引

- 📖 [使用指南](usage-guide.md) - 详细使用说明
- 🔧 [安装指导](injection-guide.md) - 劫持安装步骤
- ⚙️ [配置说明](package-contribution.json) - VSCode扩展配置

## 🔄 更新日志

### v1.0.0
- ✅ 基础token管理功能
- ✅ 侧边栏UI界面
- ✅ 一键更新功能
- ✅ 完整文档

## 📞 技术支持

如遇问题：
1. 查看相关文档
2. 检查控制台日志
3. 确认配置正确
4. 验证后端API正常

---

**⚡ 核心优势**: 最小侵入、易于维护、功能精简、安装简单
