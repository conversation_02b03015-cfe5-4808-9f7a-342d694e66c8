# 📖 Simple Token Manager 使用指南

## 🎯 功能概述

Simple Token Manager 是一个精简版的无感换号工具，核心功能：

- 🚀 一键更新 token
- 📋 查看当前 token 状态
- 🔄 管理多个可用 token
- 🎛️ 简洁的侧边栏界面

## 🔧 后端 API 要求

### API 端点

你的后端需要提供以下端点：

#### 1. 获取 Token 列表

```http
GET /api/tokens
Authorization: Bearer your_secret_password
Content-Type: application/json
User-Agent: VSCode-SimpleTokenManager/1.0.0
```

**响应格式：**

```json
{
  "tokens": [
    {
      "id": "token_1",
      "accessToken": "实际的JWT token字符串",
      "tenantURL": "https://d5.api.augmentcode.com/"
    },
    {
      "id": "token_2",
      "accessToken": "另一个JWT token字符串",
      "tenantURL": "https://d5.api.augmentcode.com/"
    }
  ]
}
```

### 配置修改

在 `token-manager.js` 文件顶部修改配置：

```javascript
class SimpleTokenManager {
  // ========== 配置区域 - 方便修改 ==========
  static API_BASE_URL = "http://your-backend.com"; // 修改为你的后端地址
  static AUTH_PASSWORD = "your_secret_password_here"; // 修改为你的认证密码
  static DEFAULT_TENANT_URL = "https://d5.api.augmentcode.com/";
  static SCOPES = ["simple_token_manager_v1"];
  // =====================================
}
```

## 🖥️ 使用方法

### 1. 状态栏按钮（主要入口）

安装后，VSCode 右下角状态栏会出现 "🔑 Token Manager" 按钮：

- **点击按钮**：打开 Token 管理界面
- **位置**：VSCode 窗口右下角状态栏
- **图标**：🔑 Token Manager

### 2. 命令面板

按 `Ctrl+Shift+P` 打开命令面板，可以使用：

- `Simple Token: Quick Update` - 一键更新 token
- `Simple Token: Open Manager` - 打开 token 管理界面

### 3. Token 管理界面功能

打开管理界面后，可以使用：

- **🚀 一键更新 Token**: 自动选择第一个可用 token 并注入
- **📋 查看当前 Token**: 显示当前使用的 token 信息
- **🔄 刷新 Token 列表**: 从后端获取最新的 token 列表

### 4. 工作流程

```
1. 配置后端API地址和密码
   ↓
2. 点击"刷新Token列表"获取可用token
   ↓
3. 选择token或使用"一键更新"
   ↓
4. Token自动注入到Augment插件
   ↓
5. 无感使用新token
```

## 🔍 状态检查

### 查看当前 Token

点击"查看当前 Token"可以看到：

- 当前使用的 Access Token（部分显示）
- Tenant URL
- 注入状态

### 成功标志

- 绿色提示消息表示操作成功
- 红色提示消息表示操作失败
- 控制台会输出详细日志

## 🐛 故障排除

### 1. 无法获取 Token 列表

**可能原因：**

- 后端 API 地址错误
- 认证密码错误
- 后端服务未启动
- 网络连接问题

**解决方法：**

1. 检查 `token-manager.js` 中的配置
2. 确认后端服务正常运行
3. 检查网络连接
4. 查看 VSCode 开发者工具控制台的错误信息

### 2. Token 注入失败

**可能原因：**

- Token 格式不正确
- VSCode 权限问题

**解决方法：**

1. 确认后端返回的 token 是有效的 JWT
2. 重启 VSCode
3. 检查控制台错误信息

### 3. 原插件无法使用新 Token

**可能原因：**

- Token 已过期
- Tenant URL 不正确
- Token 权限不足

**解决方法：**

1. 确认 token 有效性
2. 检查 tenant URL 配置
3. 联系 token 提供方确认权限

## 📊 日志查看

### 开发者工具

1. 按 `Ctrl+Shift+I` 打开开发者工具
2. 切换到 "Console" 标签
3. 查看 `[TokenManager]` 和 `[CustomFeatures]` 前缀的日志

### 常见日志信息

```
[TokenManager] Token Manager initialized - 初始化成功
[TokenManager] 获取到 3 个可用token - 成功获取token列表
[TokenManager] Token注入成功 - token注入成功
[TokenManager] 一键更新成功，使用token: token_1 - 一键更新成功
```

## ⚙️ 高级配置

### 自定义 Scopes

默认使用与 success case 一致的 scopes，如需修改，编辑 `token-manager.js`：

```javascript
static SCOPES = ['augment_external_v1_2024'];  // 与success case保持一致
```

### 自定义超时时间

在 fetch 请求中修改 timeout 值：

```javascript
timeout: 15000; // 15秒超时
```

### 自定义 User-Agent

修改请求头中的 User-Agent：

```javascript
'User-Agent': 'YourApp-TokenManager/1.0.0'
```

## 🔒 安全注意事项

1. **密码安全**: 不要在代码中硬编码敏感密码
2. **HTTPS**: 生产环境建议使用 HTTPS
3. **Token 保护**: 确保 token 传输和存储安全
4. **权限控制**: 后端应实现适当的权限验证

## 🔄 更新和维护

### 插件更新

当 Augment 插件更新时：

1. 重新执行注入步骤
2. 检查兼容性
3. 测试功能正常

### 代码更新

修改我们的代码后：

1. 替换对应文件
2. 重启 VSCode
3. 验证功能正常

## 📞 技术支持

如果遇到问题：

1. 检查控制台日志
2. 确认配置正确
3. 验证后端 API 正常
4. 查看本文档的故障排除部分
