# 📖 Simple Token Manager 使用指南

## 🎯 功能概述

Simple Token Manager 是一个精简版的无感换号工具，核心功能：
- 🚀 一键更新token
- 📋 查看当前token状态  
- 🔄 管理多个可用token
- 🎛️ 简洁的侧边栏界面

## 🔧 后端API要求

### API端点

你的后端需要提供以下端点：

#### 1. 获取Token列表
```http
GET /api/tokens
Authorization: Bearer your_secret_password
Content-Type: application/json
User-Agent: VSCode-SimpleTokenManager/1.0.0
```

**响应格式：**
```json
{
  "tokens": [
    {
      "id": "token_1",
      "accessToken": "实际的JWT token字符串",
      "tenantURL": "https://d5.api.augmentcode.com/"
    },
    {
      "id": "token_2", 
      "accessToken": "另一个JWT token字符串",
      "tenantURL": "https://d5.api.augmentcode.com/"
    }
  ]
}
```

### 配置修改

在 `token-manager.js` 文件顶部修改配置：

```javascript
class SimpleTokenManager {
    // ========== 配置区域 - 方便修改 ==========
    static API_BASE_URL = 'http://your-backend.com';     // 修改为你的后端地址
    static AUTH_PASSWORD = 'your_secret_password_here';   // 修改为你的认证密码
    static DEFAULT_TENANT_URL = 'https://d5.api.augmentcode.com/';
    static SCOPES = ['simple_token_manager_v1'];
    // =====================================
}
```

## 🖥️ 使用方法

### 1. 侧边栏界面

安装后，VSCode侧边栏会出现 "Simple Token Manager" 视图：

- **🚀 一键更新Token**: 自动选择第一个可用token并注入
- **📋 查看当前Token**: 显示当前使用的token信息
- **🔄 刷新Token列表**: 从后端获取最新的token列表

### 2. 命令面板

按 `Ctrl+Shift+P` 打开命令面板，可以使用：

- `Simple Token: Quick Update` - 一键更新token
- `Simple Token: Open Manager` - 打开token管理界面

### 3. 工作流程

```
1. 配置后端API地址和密码
   ↓
2. 点击"刷新Token列表"获取可用token
   ↓  
3. 选择token或使用"一键更新"
   ↓
4. Token自动注入到Augment插件
   ↓
5. 无感使用新token
```

## 🔍 状态检查

### 查看当前Token
点击"查看当前Token"可以看到：
- 当前使用的Access Token（部分显示）
- Tenant URL
- 注入状态

### 成功标志
- 绿色提示消息表示操作成功
- 红色提示消息表示操作失败
- 控制台会输出详细日志

## 🐛 故障排除

### 1. 无法获取Token列表

**可能原因：**
- 后端API地址错误
- 认证密码错误
- 后端服务未启动
- 网络连接问题

**解决方法：**
1. 检查 `token-manager.js` 中的配置
2. 确认后端服务正常运行
3. 检查网络连接
4. 查看VSCode开发者工具控制台的错误信息

### 2. Token注入失败

**可能原因：**
- Token格式不正确
- VSCode权限问题

**解决方法：**
1. 确认后端返回的token是有效的JWT
2. 重启VSCode
3. 检查控制台错误信息

### 3. 原插件无法使用新Token

**可能原因：**
- Token已过期
- Tenant URL不正确
- Token权限不足

**解决方法：**
1. 确认token有效性
2. 检查tenant URL配置
3. 联系token提供方确认权限

## 📊 日志查看

### 开发者工具
1. 按 `Ctrl+Shift+I` 打开开发者工具
2. 切换到 "Console" 标签
3. 查看 `[TokenManager]` 和 `[CustomFeatures]` 前缀的日志

### 常见日志信息
```
[TokenManager] Token Manager initialized - 初始化成功
[TokenManager] 获取到 3 个可用token - 成功获取token列表
[TokenManager] Token注入成功 - token注入成功
[TokenManager] 一键更新成功，使用token: token_1 - 一键更新成功
```

## ⚙️ 高级配置

### 自定义Scopes
如果需要自定义scopes，修改 `token-manager.js`：
```javascript
static SCOPES = ['your_custom_scope_v1'];
```

### 自定义超时时间
在fetch请求中修改timeout值：
```javascript
timeout: 15000  // 15秒超时
```

### 自定义User-Agent
修改请求头中的User-Agent：
```javascript
'User-Agent': 'YourApp-TokenManager/1.0.0'
```

## 🔒 安全注意事项

1. **密码安全**: 不要在代码中硬编码敏感密码
2. **HTTPS**: 生产环境建议使用HTTPS
3. **Token保护**: 确保token传输和存储安全
4. **权限控制**: 后端应实现适当的权限验证

## 🔄 更新和维护

### 插件更新
当Augment插件更新时：
1. 重新执行注入步骤
2. 检查兼容性
3. 测试功能正常

### 代码更新
修改我们的代码后：
1. 替换对应文件
2. 重启VSCode
3. 验证功能正常

## 📞 技术支持

如果遇到问题：
1. 检查控制台日志
2. 确认配置正确
3. 验证后端API正常
4. 查看本文档的故障排除部分
