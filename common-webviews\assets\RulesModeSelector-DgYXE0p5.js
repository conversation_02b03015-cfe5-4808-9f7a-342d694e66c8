import{A as ee,C as k,X as Q,Y as v,m as h,K as B,_ as e,$ as ae,D as se,F as te,G as A,H as F,L as f,b as a,I as le,a2 as oe,V as y,J as E,Q as U,W,t as re,M as Y,a3 as ie,a4 as ne,a5 as de}from"./SpinnerAugment-BY2Lraps.js";import{e as ce,i as ue}from"./IconButtonAugment-B8y0FMb_.js";import{A as ve,D as d}from"./index-78K9HN9C.js";import{B as G}from"./ButtonAugment-BoJU5mQc.js";import{C as he}from"./chevron-down-CVLGkBkY.js";import{T as fe}from"./CardAugment-BaFOe6RO.js";import{R}from"./message-broker-BauNv3yh.js";var pe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),$e=E("<!> <!>",1),ge=E("<!> <!>",1);function Se(J,T){ee(T,!1);const[q,K]=oe(),p=()=>ne(e(C),"$focusedIndex",q),w=h(),S=h(),s=h();let V=k(T,"onSave",8),i=k(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=h(void 0),I=h(()=>{});Q(()=>B(i()),()=>{v(w,i().path)}),Q(()=>B(i()),()=>{v(S,i().type)}),Q(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var z=te(),X=A(z),j=t=>{fe(t,{content:"Workspace guidelines are always applied",children:(r,Z)=>{G(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=y("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},O=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){de(v(C,r),"$focusedIndex",q)},children:(r,Z)=>{var m=ge(),D=A(m);d.Trigger(D,{children:(M,P)=>{var c=pe(),$=re(c);G($,{color:"neutral",size:1,variant:"soft",children:(u,_)=>{var l=y();U(()=>W(l,(e(s),f(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,_)=>{he(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=Y(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,P)=>{var c=$e(),$=A(c);ce($,1,()=>x,ue,(l,o)=>{const g=ie(()=>(e(s),e(o),f(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await V()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e(g)},children:(n,b)=>{var N=y();U(()=>W(N,(e(o),f(()=>e(o).label)))),a(n,N)},$$slots:{default:!0}})});var u=Y($,2),_=l=>{var o=me(),g=A(o);d.Separator(g,{});var n=Y(g,2);d.Label(n,{children:(b,N)=>{var H=y();U(()=>W(H,(p(),e(s),f(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,H)},$$slots:{default:!0}}),a(l,o)};F(u,l=>{(p()!==void 0||e(s))&&l(_)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};F(X,t=>{e(w),f(()=>e(w)===ve)?t(j):t(O,!1)}),a(J,z),le(),K()}export{Se as R};
