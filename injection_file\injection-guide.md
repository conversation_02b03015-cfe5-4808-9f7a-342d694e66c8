# 🚀 Extension.js 劫持指导

## 📋 概述

本指导将教你如何最小幅度修改 `out/extension.js` 来实现无感换号功能。

## 🎯 核心原理

通过劫持原插件的 `activate` 函数，在原功能基础上添加我们的token管理功能。

## 📁 文件位置

找到VSCode中Augment插件的安装位置：
- **Windows**: `%USERPROFILE%\.vscode\extensions\augmentcode.augment-*\out\extension.js`
- **macOS**: `~/.vscode/extensions/augmentcode.augment-*/out/extension.js`
- **Linux**: `~/.vscode/extensions/augmentcode.augment-*/out/extension.js`

## 🔧 劫持步骤

### 1. 备份原文件
```bash
# 备份原始文件
cp extension.js extension.js.backup
```

### 2. 复制我们的文件
将 `token-manager.js` 和 `custom-features.js` 复制到插件的 `out` 目录下：
```bash
# 复制文件到插件目录
cp token-manager.js /path/to/augment/out/
cp custom-features.js /path/to/augment/out/
```

### 3. 修改 extension.js

在 `extension.js` 文件的**最末尾**添加以下代码：

```javascript
// ========== Simple Token Manager 注入代码 ==========
// 保存原始exports
const originalExports = module.exports ? { ...module.exports } : {};

// 引入我们的模块
const CustomFeatures = require('./custom-features');
let customFeatures = null;

// 自定义activate函数
async function customActivate(context) {
    try {
        // 先调用原始activate
        if (originalExports.activate && typeof originalExports.activate === 'function') {
            await originalExports.activate(context);
        }
        
        // 延迟初始化我们的功能，避免冲突
        setTimeout(async () => {
            try {
                customFeatures = new CustomFeatures();
                await customFeatures.initialize(context);
                console.log('[SimpleTokenManager] 初始化成功');
            } catch (error) {
                console.error('[SimpleTokenManager] 初始化失败:', error);
            }
        }, 1000); // 延迟1秒
        
    } catch (error) {
        console.error('[SimpleTokenManager] activate失败:', error);
        // 如果我们的代码出错，确保原插件仍能正常工作
        if (originalExports.activate && typeof originalExports.activate === 'function') {
            await originalExports.activate(context);
        }
    }
}

// 自定义deactivate函数
function customDeactivate() {
    try {
        if (customFeatures) {
            customFeatures.dispose();
        }
        if (originalExports.deactivate && typeof originalExports.deactivate === 'function') {
            originalExports.deactivate();
        }
    } catch (error) {
        console.error('[SimpleTokenManager] deactivate失败:', error);
    }
}

// 劫持module.exports
module.exports = {
    ...originalExports,
    activate: customActivate,
    deactivate: customDeactivate
};
// ========== Simple Token Manager 注入代码结束 ==========
```

## 🎯 关键点说明

### 1. **位置重要性**
- 必须在文件**最末尾**添加
- 不要修改文件中间的任何内容

### 2. **错误隔离**
- 使用 try-catch 包装所有代码
- 确保我们的代码出错时不影响原插件

### 3. **时机控制**
- 延迟1秒初始化，避免与原插件冲突
- 先调用原始activate，再初始化我们的功能

### 4. **兼容性**
- 保存原始exports，确保其他功能不受影响
- 使用展开运算符保持原有属性

## 🔍 验证安装

### 1. 重启VSCode
修改后需要重启VSCode使更改生效。

### 2. 检查控制台
打开VSCode开发者工具 (`Ctrl+Shift+I`)，查看控制台是否有：
```
[SimpleTokenManager] 初始化成功
```

### 3. 检查命令面板
按 `Ctrl+Shift+P`，搜索 "Simple Token"，应该能看到我们的命令。

### 4. 检查侧边栏
在VSCode侧边栏应该能看到 "Simple Token Manager" 视图。

## ⚠️ 注意事项

1. **插件更新**: Augment插件更新时会覆盖我们的修改，需要重新注入
2. **备份重要**: 始终保留原文件备份
3. **测试环境**: 建议先在测试环境验证
4. **权限问题**: 确保有写入插件目录的权限

## 🔄 回滚方法

如果出现问题，可以快速回滚：
```bash
# 恢复原始文件
cp extension.js.backup extension.js
```

## 🐛 常见问题

### Q: 注入后VSCode无法启动？
A: 检查语法错误，恢复备份文件

### Q: 看不到我们的功能？
A: 检查文件路径是否正确，重启VSCode

### Q: 原插件功能异常？
A: 检查是否正确保存了原始exports

## 📞 调试技巧

1. 使用 `console.log` 输出调试信息
2. 检查VSCode开发者工具的控制台
3. 确认文件路径和权限正确
