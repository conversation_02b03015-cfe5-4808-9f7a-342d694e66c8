import{_ as et,g as Ye,s as ke,a as He,b as Xe,o as ze,p as Ve,I as Be,aa as We,x as je,l as Ie,al as ce,c as oe,aG as le,d as $e,G as qe,aH as Ze,aI as Je}from"./AugmentMessage-FtcicXdY.js";import{p as Ke}from"./chunk-TMUBEWPD-CvqON8kH.js";import{I as Qe}from"./chunk-KFBOBJHC-DI40Oikd.js";import{p as tr}from"./gitGraph-YCYPL57B-CCf0cCGI.js";import{c as _e}from"./cytoscape.esm-ClSAe4oe.js";import{y as er}from"./SpinnerAugment-BY2Lraps.js";import"./CalloutAugment-BPYQDfw6.js";import"./CardAugment-BaFOe6RO.js";import"./IconButtonAugment-B8y0FMb_.js";import"./index-78K9HN9C.js";import"./async-messaging-BnOo7nYC.js";import"./message-broker-BauNv3yh.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";import"./BaseTextInput-C64uUToe.js";import"./index-CoHT-xzg.js";import"./diff-operations-TFiyZoZ6.js";import"./svelte-component-DfqKRK9G.js";import"./Filespan-7QsRDmJG.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BPm23rLE.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-B-IBFaHN.js";import"./await-CoczQRp_.js";import"./OpenFileButton-DL6LWlGL.js";import"./chat-model-context-C9JFkoqk.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-XI3B217g.js";import"./ra-diff-ops-model-3WeVBTrk.js";import"./TextAreaAugment-BkN7aH6v.js";import"./ButtonAugment-BoJU5mQc.js";import"./CollapseButtonAugment-CllkyJKm.js";import"./trash-can-Col-hlUX.js";import"./MaterialIcon-CKuUXxrb.js";import"./feedback-rating-BX5Icwas.js";import"./copy-DdR1jezc.js";import"./ellipsis-BVNflcFA.js";import"./LanguageIcon-BvE8QmB9.js";import"./chevron-down-CVLGkBkY.js";import"./index-ALhsmmIa.js";import"./augment-logo-DFXa-EF4.js";import"./pen-to-square-BKF2K8ly.js";import"./_baseUniq-BPN2Ft2x.js";import"./_basePickBy-DRSktJHC.js";import"./clone-QmpUOu2l.js";var ve,Ee,rr={exports:{}},Ne={exports:{}},Te={exports:{}};function nr(){return ve||(ve=1,A=function(){return function(L){var b={};function T(g){if(b[g])return b[g].exports;var i=b[g]={i:g,l:!1,exports:{}};return L[g].call(i.exports,i,i.exports,T),i.l=!0,i.exports}return T.m=L,T.c=b,T.i=function(g){return g},T.d=function(g,i,a){T.o(g,i)||Object.defineProperty(g,i,{configurable:!1,enumerable:!0,get:a})},T.n=function(g){var i=g&&g.__esModule?function(){return g.default}:function(){return g};return T.d(i,"a",i),i},T.o=function(g,i){return Object.prototype.hasOwnProperty.call(g,i)},T.p="",T(T.s=28)}([function(L,b,T){function g(){}g.QUALITY=1,g.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,g.DEFAULT_INCREMENTAL=!1,g.DEFAULT_ANIMATION_ON_LAYOUT=!0,g.DEFAULT_ANIMATION_DURING_LAYOUT=!1,g.DEFAULT_ANIMATION_PERIOD=50,g.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,g.DEFAULT_GRAPH_MARGIN=15,g.NODE_DIMENSIONS_INCLUDE_LABELS=!1,g.SIMPLE_NODE_SIZE=40,g.SIMPLE_NODE_HALF_SIZE=g.SIMPLE_NODE_SIZE/2,g.EMPTY_COMPOUND_NODE_SIZE=40,g.MIN_EDGE_LENGTH=1,g.WORLD_BOUNDARY=1e6,g.INITIAL_WORLD_BOUNDARY=g.WORLD_BOUNDARY/1e3,g.WORLD_CENTER_X=1200,g.WORLD_CENTER_Y=900,L.exports=g},function(L,b,T){var g=T(2),i=T(8),a=T(9);function t(e,l,h){g.call(this,h),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=h,this.bendpoints=[],this.source=e,this.target=l}for(var r in t.prototype=Object.create(g.prototype),g)t[r]=g[r];t.prototype.getSource=function(){return this.source},t.prototype.getTarget=function(){return this.target},t.prototype.isInterGraph=function(){return this.isInterGraph},t.prototype.getLength=function(){return this.length},t.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},t.prototype.getBendpoints=function(){return this.bendpoints},t.prototype.getLca=function(){return this.lca},t.prototype.getSourceInLca=function(){return this.sourceInLca},t.prototype.getTargetInLca=function(){return this.targetInLca},t.prototype.getOtherEnd=function(e){if(this.source===e)return this.target;if(this.target===e)return this.source;throw"Node is not incident with this edge"},t.prototype.getOtherEndInGraph=function(e,l){for(var h=this.getOtherEnd(e),d=l.getGraphManager().getRoot();;){if(h.getOwner()==l)return h;if(h.getOwner()==d)break;h=h.getOwner().getParent()}return null},t.prototype.updateLength=function(){var e=new Array(4);this.isOverlapingSourceAndTarget=i.getIntersection(this.target.getRect(),this.source.getRect(),e),this.isOverlapingSourceAndTarget||(this.lengthX=e[0]-e[2],this.lengthY=e[1]-e[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},t.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},L.exports=t},function(L,b,T){L.exports=function(g){this.vGraphObject=g}},function(L,b,T){var g=T(2),i=T(10),a=T(13),t=T(0),r=T(16),e=T(5);function l(d,o,c,n){c==null&&n==null&&(n=o),g.call(this,n),d.graphManager!=null&&(d=d.graphManager),this.estimatedSize=i.MIN_VALUE,this.inclusionTreeDepth=i.MAX_VALUE,this.vGraphObject=n,this.edges=[],this.graphManager=d,this.rect=c!=null&&o!=null?new a(o.x,o.y,c.width,c.height):new a}for(var h in l.prototype=Object.create(g.prototype),g)l[h]=g[h];l.prototype.getEdges=function(){return this.edges},l.prototype.getChild=function(){return this.child},l.prototype.getOwner=function(){return this.owner},l.prototype.getWidth=function(){return this.rect.width},l.prototype.setWidth=function(d){this.rect.width=d},l.prototype.getHeight=function(){return this.rect.height},l.prototype.setHeight=function(d){this.rect.height=d},l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},l.prototype.getCenter=function(){return new e(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},l.prototype.getLocation=function(){return new e(this.rect.x,this.rect.y)},l.prototype.getRect=function(){return this.rect},l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},l.prototype.setRect=function(d,o){this.rect.x=d.x,this.rect.y=d.y,this.rect.width=o.width,this.rect.height=o.height},l.prototype.setCenter=function(d,o){this.rect.x=d-this.rect.width/2,this.rect.y=o-this.rect.height/2},l.prototype.setLocation=function(d,o){this.rect.x=d,this.rect.y=o},l.prototype.moveBy=function(d,o){this.rect.x+=d,this.rect.y+=o},l.prototype.getEdgeListToNode=function(d){var o=[],c=this;return c.edges.forEach(function(n){if(n.target==d){if(n.source!=c)throw"Incorrect edge source!";o.push(n)}}),o},l.prototype.getEdgesBetween=function(d){var o=[],c=this;return c.edges.forEach(function(n){if(n.source!=c&&n.target!=c)throw"Incorrect edge source and/or target";n.target!=d&&n.source!=d||o.push(n)}),o},l.prototype.getNeighborsList=function(){var d=new Set,o=this;return o.edges.forEach(function(c){if(c.source==o)d.add(c.target);else{if(c.target!=o)throw"Incorrect incidency!";d.add(c.source)}}),d},l.prototype.withChildren=function(){var d=new Set;if(d.add(this),this.child!=null)for(var o=this.child.getNodes(),c=0;c<o.length;c++)o[c].withChildren().forEach(function(n){d.add(n)});return d},l.prototype.getNoOfChildren=function(){var d=0;if(this.child==null)d=1;else for(var o=this.child.getNodes(),c=0;c<o.length;c++)d+=o[c].getNoOfChildren();return d==0&&(d=1),d},l.prototype.getEstimatedSize=function(){if(this.estimatedSize==i.MIN_VALUE)throw"assert failed";return this.estimatedSize},l.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},l.prototype.scatter=function(){var d,o,c=-t.INITIAL_WORLD_BOUNDARY,n=t.INITIAL_WORLD_BOUNDARY;d=t.WORLD_CENTER_X+r.nextDouble()*(n-c)+c;var y=-t.INITIAL_WORLD_BOUNDARY,v=t.INITIAL_WORLD_BOUNDARY;o=t.WORLD_CENTER_Y+r.nextDouble()*(v-y)+y,this.rect.x=d,this.rect.y=o},l.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var d=this.getChild();if(d.updateBounds(!0),this.rect.x=d.getLeft(),this.rect.y=d.getTop(),this.setWidth(d.getRight()-d.getLeft()),this.setHeight(d.getBottom()-d.getTop()),t.NODE_DIMENSIONS_INCLUDE_LABELS){var o=d.getRight()-d.getLeft(),c=d.getBottom()-d.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(o+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>o?(this.rect.x-=(this.labelWidth-o)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(o+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(c+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>c?(this.rect.y-=(this.labelHeight-c)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(c+this.labelHeight))}}},l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==i.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},l.prototype.transform=function(d){var o=this.rect.x;o>t.WORLD_BOUNDARY?o=t.WORLD_BOUNDARY:o<-t.WORLD_BOUNDARY&&(o=-t.WORLD_BOUNDARY);var c=this.rect.y;c>t.WORLD_BOUNDARY?c=t.WORLD_BOUNDARY:c<-t.WORLD_BOUNDARY&&(c=-t.WORLD_BOUNDARY);var n=new e(o,c),y=d.inverseTransformPoint(n);this.setLocation(y.x,y.y)},l.prototype.getLeft=function(){return this.rect.x},l.prototype.getRight=function(){return this.rect.x+this.rect.width},l.prototype.getTop=function(){return this.rect.y},l.prototype.getBottom=function(){return this.rect.y+this.rect.height},l.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},L.exports=l},function(L,b,T){var g=T(0);function i(){}for(var a in g)i[a]=g[a];i.MAX_ITERATIONS=2500,i.DEFAULT_EDGE_LENGTH=50,i.DEFAULT_SPRING_STRENGTH=.45,i.DEFAULT_REPULSION_STRENGTH=4500,i.DEFAULT_GRAVITY_STRENGTH=.4,i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,i.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,i.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,i.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,i.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,i.COOLING_ADAPTATION_FACTOR=.33,i.ADAPTATION_LOWER_NODE_LIMIT=1e3,i.ADAPTATION_UPPER_NODE_LIMIT=5e3,i.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,i.MAX_NODE_DISPLACEMENT=3*i.MAX_NODE_DISPLACEMENT_INCREMENTAL,i.MIN_REPULSION_DIST=i.DEFAULT_EDGE_LENGTH/10,i.CONVERGENCE_CHECK_PERIOD=100,i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,i.MIN_EDGE_LENGTH=1,i.GRID_CALCULATION_CHECK_PERIOD=10,L.exports=i},function(L,b,T){function g(i,a){i==null&&a==null?(this.x=0,this.y=0):(this.x=i,this.y=a)}g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.setX=function(i){this.x=i},g.prototype.setY=function(i){this.y=i},g.prototype.getDifference=function(i){return new DimensionD(this.x-i.x,this.y-i.y)},g.prototype.getCopy=function(){return new g(this.x,this.y)},g.prototype.translate=function(i){return this.x+=i.width,this.y+=i.height,this},L.exports=g},function(L,b,T){var g=T(2),i=T(10),a=T(0),t=T(7),r=T(3),e=T(1),l=T(13),h=T(12),d=T(11);function o(n,y,v){g.call(this,v),this.estimatedSize=i.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=n,y!=null&&y instanceof t?this.graphManager=y:y!=null&&y instanceof Layout&&(this.graphManager=y.graphManager)}for(var c in o.prototype=Object.create(g.prototype),g)o[c]=g[c];o.prototype.getNodes=function(){return this.nodes},o.prototype.getEdges=function(){return this.edges},o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getParent=function(){return this.parent},o.prototype.getLeft=function(){return this.left},o.prototype.getRight=function(){return this.right},o.prototype.getTop=function(){return this.top},o.prototype.getBottom=function(){return this.bottom},o.prototype.isConnected=function(){return this.isConnected},o.prototype.add=function(n,y,v){if(y==null&&v==null){var p=n;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(p)>-1)throw"Node already in graph!";return p.owner=this,this.getNodes().push(p),p}var N=n;if(!(this.getNodes().indexOf(y)>-1&&this.getNodes().indexOf(v)>-1))throw"Source or target not in graph!";if(y.owner!=v.owner||y.owner!=this)throw"Both owners must be this graph!";return y.owner!=v.owner?null:(N.source=y,N.target=v,N.isInterGraph=!1,this.getEdges().push(N),y.edges.push(N),v!=y&&v.edges.push(N),N)},o.prototype.remove=function(n){var y=n;if(n instanceof r){if(y==null)throw"Node is null!";if(y.owner==null||y.owner!=this)throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var v=y.edges.slice(),p=v.length,N=0;N<p;N++)(_=v[N]).isInterGraph?this.graphManager.remove(_):_.source.owner.remove(_);if((P=this.nodes.indexOf(y))==-1)throw"Node not in owner node list!";this.nodes.splice(P,1)}else if(n instanceof e){var _;if((_=n)==null)throw"Edge is null!";if(_.source==null||_.target==null)throw"Source and/or target is null!";if(_.source.owner==null||_.target.owner==null||_.source.owner!=this||_.target.owner!=this)throw"Source and/or target owner is invalid!";var P,U=_.source.edges.indexOf(_),F=_.target.edges.indexOf(_);if(!(U>-1&&F>-1))throw"Source and/or target doesn't know this edge!";if(_.source.edges.splice(U,1),_.target!=_.source&&_.target.edges.splice(F,1),(P=_.source.owner.getEdges().indexOf(_))==-1)throw"Not in owner's edge list!";_.source.owner.getEdges().splice(P,1)}},o.prototype.updateLeftTop=function(){for(var n,y,v,p=i.MAX_VALUE,N=i.MAX_VALUE,_=this.getNodes(),P=_.length,U=0;U<P;U++){var F=_[U];p>(n=F.getTop())&&(p=n),N>(y=F.getLeft())&&(N=y)}return p==i.MAX_VALUE?null:(v=_[0].getParent().paddingLeft!=null?_[0].getParent().paddingLeft:this.margin,this.left=N-v,this.top=p-v,new h(this.left,this.top))},o.prototype.updateBounds=function(n){for(var y,v,p,N,_,P=i.MAX_VALUE,U=-i.MAX_VALUE,F=i.MAX_VALUE,H=-i.MAX_VALUE,V=this.nodes,x=V.length,ot=0;ot<x;ot++){var s=V[ot];n&&s.child!=null&&s.updateBounds(),P>(y=s.getLeft())&&(P=y),U<(v=s.getRight())&&(U=v),F>(p=s.getTop())&&(F=p),H<(N=s.getBottom())&&(H=N)}var E=new l(P,F,U-P,H-F);P==i.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),_=V[0].getParent().paddingLeft!=null?V[0].getParent().paddingLeft:this.margin,this.left=E.x-_,this.right=E.x+E.width+_,this.top=E.y-_,this.bottom=E.y+E.height+_},o.calculateBounds=function(n){for(var y,v,p,N,_=i.MAX_VALUE,P=-i.MAX_VALUE,U=i.MAX_VALUE,F=-i.MAX_VALUE,H=n.length,V=0;V<H;V++){var x=n[V];_>(y=x.getLeft())&&(_=y),P<(v=x.getRight())&&(P=v),U>(p=x.getTop())&&(U=p),F<(N=x.getBottom())&&(F=N)}return new l(_,U,P-_,F-U)},o.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},o.prototype.getEstimatedSize=function(){if(this.estimatedSize==i.MIN_VALUE)throw"assert failed";return this.estimatedSize},o.prototype.calcEstimatedSize=function(){for(var n=0,y=this.nodes,v=y.length,p=0;p<v;p++)n+=y[p].calcEstimatedSize();return this.estimatedSize=n==0?a.EMPTY_COMPOUND_NODE_SIZE:n/Math.sqrt(this.nodes.length),this.estimatedSize},o.prototype.updateConnected=function(){var n=this;if(this.nodes.length!=0){var y,v,p=new d,N=new Set,_=this.nodes[0];for(_.withChildren().forEach(function(H){p.push(H),N.add(H)});p.length!==0;)for(var P=(y=(_=p.shift()).getEdges()).length,U=0;U<P;U++)(v=y[U].getOtherEndInGraph(_,this))==null||N.has(v)||v.withChildren().forEach(function(H){p.push(H),N.add(H)});if(this.isConnected=!1,N.size>=this.nodes.length){var F=0;N.forEach(function(H){H.owner==n&&F++}),F==this.nodes.length&&(this.isConnected=!0)}}else this.isConnected=!0},L.exports=o},function(L,b,T){var g,i=T(1);function a(t){g=T(6),this.layout=t,this.graphs=[],this.edges=[]}a.prototype.addRoot=function(){var t=this.layout.newGraph(),r=this.layout.newNode(null),e=this.add(t,r);return this.setRootGraph(e),this.rootGraph},a.prototype.add=function(t,r,e,l,h){if(e==null&&l==null&&h==null){if(t==null)throw"Graph is null!";if(r==null)throw"Parent node is null!";if(this.graphs.indexOf(t)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(t),t.parent!=null)throw"Already has a parent!";if(r.child!=null)throw"Already has a child!";return t.parent=r,r.child=t,t}h=e,e=t;var d=(l=r).getOwner(),o=h.getOwner();if(d==null||d.getGraphManager()!=this)throw"Source not in this graph mgr!";if(o==null||o.getGraphManager()!=this)throw"Target not in this graph mgr!";if(d==o)return e.isInterGraph=!1,d.add(e,l,h);if(e.isInterGraph=!0,e.source=l,e.target=h,this.edges.indexOf(e)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(e),e.source==null||e.target==null)throw"Edge source and/or target is null!";if(e.source.edges.indexOf(e)!=-1||e.target.edges.indexOf(e)!=-1)throw"Edge already in source and/or target incidency list!";return e.source.edges.push(e),e.target.edges.push(e),e},a.prototype.remove=function(t){if(t instanceof g){var r=t;if(r.getGraphManager()!=this)throw"Graph not in this graph mgr";if(r!=this.rootGraph&&(r.parent==null||r.parent.graphManager!=this))throw"Invalid parent node!";for(var e,l=[],h=(l=l.concat(r.getEdges())).length,d=0;d<h;d++)e=l[d],r.remove(e);var o,c=[];for(h=(c=c.concat(r.getNodes())).length,d=0;d<h;d++)o=c[d],r.remove(o);r==this.rootGraph&&this.setRootGraph(null);var n=this.graphs.indexOf(r);this.graphs.splice(n,1),r.parent=null}else if(t instanceof i){if((e=t)==null)throw"Edge is null!";if(!e.isInterGraph)throw"Not an inter-graph edge!";if(e.source==null||e.target==null)throw"Source and/or target is null!";if(e.source.edges.indexOf(e)==-1||e.target.edges.indexOf(e)==-1)throw"Source and/or target doesn't know this edge!";if(n=e.source.edges.indexOf(e),e.source.edges.splice(n,1),n=e.target.edges.indexOf(e),e.target.edges.splice(n,1),e.source.owner==null||e.source.owner.getGraphManager()==null)throw"Edge owner graph or owner graph manager is null!";if(e.source.owner.getGraphManager().edges.indexOf(e)==-1)throw"Not in owner graph manager's edge list!";n=e.source.owner.getGraphManager().edges.indexOf(e),e.source.owner.getGraphManager().edges.splice(n,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(this.allNodes==null){for(var t=[],r=this.getGraphs(),e=r.length,l=0;l<e;l++)t=t.concat(r[l].getNodes());this.allNodes=t}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(this.allEdges==null){var t=[],r=this.getGraphs();r.length;for(var e=0;e<r.length;e++)t=t.concat(r[e].getEdges());t=t.concat(this.edges),this.allEdges=t}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(t){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=t},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=t,t.parent==null&&(t.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(t,r){if(t==null||r==null)throw"assert failed";if(t==r)return!0;for(var e,l=t.getOwner();(e=l.getParent())!=null;){if(e==r)return!0;if((l=e.getOwner())==null)break}for(l=r.getOwner();(e=l.getParent())!=null;){if(e==t)return!0;if((l=e.getOwner())==null)break}return!1},a.prototype.calcLowestCommonAncestors=function(){for(var t,r,e,l,h,d=this.getAllEdges(),o=d.length,c=0;c<o;c++)if(r=(t=d[c]).source,e=t.target,t.lca=null,t.sourceInLca=r,t.targetInLca=e,r!=e){for(l=r.getOwner();t.lca==null;){for(t.targetInLca=e,h=e.getOwner();t.lca==null;){if(h==l){t.lca=h;break}if(h==this.rootGraph)break;if(t.lca!=null)throw"assert failed";t.targetInLca=h.getParent(),h=t.targetInLca.getOwner()}if(l==this.rootGraph)break;t.lca==null&&(t.sourceInLca=l.getParent(),l=t.sourceInLca.getOwner())}if(t.lca==null)throw"assert failed"}else t.lca=r.getOwner()},a.prototype.calcLowestCommonAncestor=function(t,r){if(t==r)return t.getOwner();for(var e=t.getOwner();e!=null;){for(var l=r.getOwner();l!=null;){if(l==e)return l;l=l.getParent().getOwner()}e=e.getParent().getOwner()}return e},a.prototype.calcInclusionTreeDepths=function(t,r){var e;t==null&&r==null&&(t=this.rootGraph,r=1);for(var l=t.getNodes(),h=l.length,d=0;d<h;d++)(e=l[d]).inclusionTreeDepth=r,e.child!=null&&this.calcInclusionTreeDepths(e.child,r+1)},a.prototype.includesInvalidEdge=function(){for(var t,r=[],e=this.edges.length,l=0;l<e;l++)t=this.edges[l],this.isOneAncestorOfOther(t.source,t.target)&&r.push(t);for(l=0;l<r.length;l++)this.remove(r[l]);return!1},L.exports=a},function(L,b,T){var g=T(12);function i(){}i.calcSeparationAmount=function(a,t,r,e){if(!a.intersects(t))throw"assert failed";var l=new Array(2);this.decideDirectionsForOverlappingNodes(a,t,l),r[0]=Math.min(a.getRight(),t.getRight())-Math.max(a.x,t.x),r[1]=Math.min(a.getBottom(),t.getBottom())-Math.max(a.y,t.y),a.getX()<=t.getX()&&a.getRight()>=t.getRight()?r[0]+=Math.min(t.getX()-a.getX(),a.getRight()-t.getRight()):t.getX()<=a.getX()&&t.getRight()>=a.getRight()&&(r[0]+=Math.min(a.getX()-t.getX(),t.getRight()-a.getRight())),a.getY()<=t.getY()&&a.getBottom()>=t.getBottom()?r[1]+=Math.min(t.getY()-a.getY(),a.getBottom()-t.getBottom()):t.getY()<=a.getY()&&t.getBottom()>=a.getBottom()&&(r[1]+=Math.min(a.getY()-t.getY(),t.getBottom()-a.getBottom()));var h=Math.abs((t.getCenterY()-a.getCenterY())/(t.getCenterX()-a.getCenterX()));t.getCenterY()===a.getCenterY()&&t.getCenterX()===a.getCenterX()&&(h=1);var d=h*r[0],o=r[1]/h;r[0]<o?o=r[0]:d=r[1],r[0]=-1*l[0]*(o/2+e),r[1]=-1*l[1]*(d/2+e)},i.decideDirectionsForOverlappingNodes=function(a,t,r){a.getCenterX()<t.getCenterX()?r[0]=-1:r[0]=1,a.getCenterY()<t.getCenterY()?r[1]=-1:r[1]=1},i.getIntersection2=function(a,t,r){var e=a.getCenterX(),l=a.getCenterY(),h=t.getCenterX(),d=t.getCenterY();if(a.intersects(t))return r[0]=e,r[1]=l,r[2]=h,r[3]=d,!0;var o=a.getX(),c=a.getY(),n=a.getRight(),y=a.getX(),v=a.getBottom(),p=a.getRight(),N=a.getWidthHalf(),_=a.getHeightHalf(),P=t.getX(),U=t.getY(),F=t.getRight(),H=t.getX(),V=t.getBottom(),x=t.getRight(),ot=t.getWidthHalf(),s=t.getHeightHalf(),E=!1,u=!1;if(e===h){if(l>d)return r[0]=e,r[1]=c,r[2]=h,r[3]=V,!1;if(l<d)return r[0]=e,r[1]=v,r[2]=h,r[3]=U,!1}else if(l===d){if(e>h)return r[0]=o,r[1]=l,r[2]=F,r[3]=d,!1;if(e<h)return r[0]=n,r[1]=l,r[2]=P,r[3]=d,!1}else{var m=a.height/a.width,f=t.height/t.width,w=(d-l)/(h-e),C=void 0,G=void 0,R=void 0,M=void 0,X=void 0,S=void 0;if(-m===w?e>h?(r[0]=y,r[1]=v,E=!0):(r[0]=n,r[1]=c,E=!0):m===w&&(e>h?(r[0]=o,r[1]=c,E=!0):(r[0]=p,r[1]=v,E=!0)),-f===w?h>e?(r[2]=H,r[3]=V,u=!0):(r[2]=F,r[3]=U,u=!0):f===w&&(h>e?(r[2]=P,r[3]=U,u=!0):(r[2]=x,r[3]=V,u=!0)),E&&u)return!1;if(e>h?l>d?(C=this.getCardinalDirection(m,w,4),G=this.getCardinalDirection(f,w,2)):(C=this.getCardinalDirection(-m,w,3),G=this.getCardinalDirection(-f,w,1)):l>d?(C=this.getCardinalDirection(-m,w,1),G=this.getCardinalDirection(-f,w,3)):(C=this.getCardinalDirection(m,w,2),G=this.getCardinalDirection(f,w,4)),!E)switch(C){case 1:M=c,R=e+-_/w,r[0]=R,r[1]=M;break;case 2:R=p,M=l+N*w,r[0]=R,r[1]=M;break;case 3:M=v,R=e+_/w,r[0]=R,r[1]=M;break;case 4:R=y,M=l+-N*w,r[0]=R,r[1]=M}if(!u)switch(G){case 1:S=U,X=h+-s/w,r[2]=X,r[3]=S;break;case 2:X=x,S=d+ot*w,r[2]=X,r[3]=S;break;case 3:S=V,X=h+s/w,r[2]=X,r[3]=S;break;case 4:X=H,S=d+-ot*w,r[2]=X,r[3]=S}}return!1},i.getCardinalDirection=function(a,t,r){return a>t?r:1+r%4},i.getIntersection=function(a,t,r,e){if(e==null)return this.getIntersection2(a,t,r);var l,h,d,o,c,n,y,v=a.x,p=a.y,N=t.x,_=t.y,P=r.x,U=r.y,F=e.x,H=e.y;return(y=(l=_-p)*(o=P-F)-(h=H-U)*(d=v-N))==0?null:new g((d*(n=F*U-P*H)-o*(c=N*p-v*_))/y,(h*c-l*n)/y)},i.angleOfVector=function(a,t,r,e){var l=void 0;return a!==r?(l=Math.atan((e-t)/(r-a)),r<a?l+=Math.PI:e<t&&(l+=this.TWO_PI)):l=e<t?this.ONE_AND_HALF_PI:this.HALF_PI,l},i.doIntersect=function(a,t,r,e){var l=a.x,h=a.y,d=t.x,o=t.y,c=r.x,n=r.y,y=e.x,v=e.y,p=(d-l)*(v-n)-(y-c)*(o-h);if(p===0)return!1;var N=((v-n)*(y-l)+(c-y)*(v-h))/p,_=((h-o)*(y-l)+(d-l)*(v-h))/p;return 0<N&&N<1&&0<_&&_<1},i.findCircleLineIntersections=function(a,t,r,e,l,h,d){var o=(r-a)*(r-a)+(e-t)*(e-t),c=2*((a-l)*(r-a)+(t-h)*(e-t)),n=(a-l)*(a-l)+(t-h)*(t-h)-d*d;if(c*c-4*o*n>=0){var y=(-c+Math.sqrt(c*c-4*o*n))/(2*o),v=(-c-Math.sqrt(c*c-4*o*n))/(2*o);return y>=0&&y<=1?[y]:v>=0&&v<=1?[v]:null}return null},i.HALF_PI=.5*Math.PI,i.ONE_AND_HALF_PI=1.5*Math.PI,i.TWO_PI=2*Math.PI,i.THREE_PI=3*Math.PI,L.exports=i},function(L,b,T){function g(){}g.sign=function(i){return i>0?1:i<0?-1:0},g.floor=function(i){return i<0?Math.ceil(i):Math.floor(i)},g.ceil=function(i){return i<0?Math.floor(i):Math.ceil(i)},L.exports=g},function(L,b,T){function g(){}g.MAX_VALUE=2147483647,g.MIN_VALUE=-2147483648,L.exports=g},function(L,b,T){var g=function(){function e(l,h){for(var d=0;d<h.length;d++){var o=h[d];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,h,d){return h&&e(l.prototype,h),d&&e(l,d),l}}(),i=function(e){return{value:e,next:null,prev:null}},a=function(e,l,h,d){return e!==null?e.next=l:d.head=l,h!==null?h.prev=l:d.tail=l,l.prev=e,l.next=h,d.length++,l},t=function(e,l){var h=e.prev,d=e.next;return h!==null?h.next=d:l.head=d,d!==null?d.prev=h:l.tail=h,e.prev=e.next=null,l.length--,e},r=function(){function e(l){var h=this;(function(d,o){if(!(d instanceof o))throw new TypeError("Cannot call a class as a function")})(this,e),this.length=0,this.head=null,this.tail=null,l!=null&&l.forEach(function(d){return h.push(d)})}return g(e,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(l,h){return a(h.prev,i(l),h,this)}},{key:"insertAfter",value:function(l,h){return a(h,i(l),h.next,this)}},{key:"insertNodeBefore",value:function(l,h){return a(h.prev,l,h,this)}},{key:"insertNodeAfter",value:function(l,h){return a(h,l,h.next,this)}},{key:"push",value:function(l){return a(this.tail,i(l),null,this)}},{key:"unshift",value:function(l){return a(null,i(l),this.head,this)}},{key:"remove",value:function(l){return t(l,this)}},{key:"pop",value:function(){return t(this.tail,this).value}},{key:"popNode",value:function(){return t(this.tail,this)}},{key:"shift",value:function(){return t(this.head,this).value}},{key:"shiftNode",value:function(){return t(this.head,this)}},{key:"get_object_at",value:function(l){if(l<=this.length()){for(var h=1,d=this.head;h<l;)d=d.next,h++;return d.value}}},{key:"set_object_at",value:function(l,h){if(l<=this.length()){for(var d=1,o=this.head;d<l;)o=o.next,d++;o.value=h}}}]),e}();L.exports=r},function(L,b,T){function g(i,a,t){this.x=null,this.y=null,i==null&&a==null&&t==null?(this.x=0,this.y=0):typeof i=="number"&&typeof a=="number"&&t==null?(this.x=i,this.y=a):i.constructor.name=="Point"&&a==null&&t==null&&(t=i,this.x=t.x,this.y=t.y)}g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.getLocation=function(){return new g(this.x,this.y)},g.prototype.setLocation=function(i,a,t){i.constructor.name=="Point"&&a==null&&t==null?(t=i,this.setLocation(t.x,t.y)):typeof i=="number"&&typeof a=="number"&&t==null&&(parseInt(i)==i&&parseInt(a)==a?this.move(i,a):(this.x=Math.floor(i+.5),this.y=Math.floor(a+.5)))},g.prototype.move=function(i,a){this.x=i,this.y=a},g.prototype.translate=function(i,a){this.x+=i,this.y+=a},g.prototype.equals=function(i){if(i.constructor.name=="Point"){var a=i;return this.x==a.x&&this.y==a.y}return this==i},g.prototype.toString=function(){return new g().constructor.name+"[x="+this.x+",y="+this.y+"]"},L.exports=g},function(L,b,T){function g(i,a,t,r){this.x=0,this.y=0,this.width=0,this.height=0,i!=null&&a!=null&&t!=null&&r!=null&&(this.x=i,this.y=a,this.width=t,this.height=r)}g.prototype.getX=function(){return this.x},g.prototype.setX=function(i){this.x=i},g.prototype.getY=function(){return this.y},g.prototype.setY=function(i){this.y=i},g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(i){this.width=i},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(i){this.height=i},g.prototype.getRight=function(){return this.x+this.width},g.prototype.getBottom=function(){return this.y+this.height},g.prototype.intersects=function(i){return!(this.getRight()<i.x||this.getBottom()<i.y||i.getRight()<this.x||i.getBottom()<this.y)},g.prototype.getCenterX=function(){return this.x+this.width/2},g.prototype.getMinX=function(){return this.getX()},g.prototype.getMaxX=function(){return this.getX()+this.width},g.prototype.getCenterY=function(){return this.y+this.height/2},g.prototype.getMinY=function(){return this.getY()},g.prototype.getMaxY=function(){return this.getY()+this.height},g.prototype.getWidthHalf=function(){return this.width/2},g.prototype.getHeightHalf=function(){return this.height/2},L.exports=g},function(L,b,T){var g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};function i(){}i.lastID=0,i.createID=function(a){return i.isPrimitive(a)?a:(a.uniqueID!=null||(a.uniqueID=i.getString(),i.lastID++),a.uniqueID)},i.getString=function(a){return a==null&&(a=i.lastID),"Object#"+a},i.isPrimitive=function(a){var t=a===void 0?"undefined":g(a);return a==null||t!="object"&&t!="function"},L.exports=i},function(L,b,T){function g(c){if(Array.isArray(c)){for(var n=0,y=Array(c.length);n<c.length;n++)y[n]=c[n];return y}return Array.from(c)}var i=T(0),a=T(7),t=T(3),r=T(1),e=T(6),l=T(5),h=T(17),d=T(29);function o(c){d.call(this),this.layoutQuality=i.QUALITY,this.createBendsAsNeeded=i.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=i.DEFAULT_INCREMENTAL,this.animationOnLayout=i.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=i.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=i.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=i.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,c!=null&&(this.isRemoteUse=c)}o.RANDOM_SEED=1,o.prototype=Object.create(d.prototype),o.prototype.getGraphManager=function(){return this.graphManager},o.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},o.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},o.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},o.prototype.newGraphManager=function(){var c=new a(this);return this.graphManager=c,c},o.prototype.newGraph=function(c){return new e(null,this.graphManager,c)},o.prototype.newNode=function(c){return new t(this.graphManager,c)},o.prototype.newEdge=function(c){return new r(null,null,c)},o.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},o.prototype.runLayout=function(){var c;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),c=!this.checkLayoutSuccess()&&this.layout(),i.ANIMATE!=="during"&&(c&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,c)},o.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},o.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var c=this.graphManager.getAllEdges(),n=0;n<c.length;n++)c[n];var y=this.graphManager.getRoot().getNodes();for(n=0;n<y.length;n++)y[n];this.update(this.graphManager.getRoot())}},o.prototype.update=function(c){if(c==null)this.update2();else if(c instanceof t){var n=c;if(n.getChild()!=null)for(var y=n.getChild().getNodes(),v=0;v<y.length;v++)update(y[v]);n.vGraphObject!=null&&n.vGraphObject.update(n)}else if(c instanceof r){var p=c;p.vGraphObject!=null&&p.vGraphObject.update(p)}else if(c instanceof e){var N=c;N.vGraphObject!=null&&N.vGraphObject.update(N)}},o.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=i.QUALITY,this.animationDuringLayout=i.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=i.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=i.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=i.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=i.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=i.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},o.prototype.transform=function(c){if(c==null)this.transform(new l(0,0));else{var n=new h,y=this.graphManager.getRoot().updateLeftTop();if(y!=null){n.setWorldOrgX(c.x),n.setWorldOrgY(c.y),n.setDeviceOrgX(y.x),n.setDeviceOrgY(y.y);for(var v=this.getAllNodes(),p=0;p<v.length;p++)v[p].transform(n)}}},o.prototype.positionNodesRandomly=function(c){if(c==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var n,y,v=c.getNodes(),p=0;p<v.length;p++)(y=(n=v[p]).getChild())==null||y.getNodes().length==0?n.scatter():(this.positionNodesRandomly(y),n.updateBounds())},o.prototype.getFlatForest=function(){for(var c=[],n=!0,y=this.graphManager.getRoot().getNodes(),v=!0,p=0;p<y.length;p++)y[p].getChild()!=null&&(v=!1);if(!v)return c;var N=new Set,_=[],P=new Map,U=[];for(U=U.concat(y);U.length>0&&n;){for(_.push(U[0]);_.length>0&&n;){var F=_[0];_.splice(0,1),N.add(F);var H=F.getEdges();for(p=0;p<H.length;p++){var V=H[p].getOtherEnd(F);if(P.get(F)!=V){if(N.has(V)){n=!1;break}_.push(V),P.set(V,F)}}}if(n){var x=[].concat(g(N));for(c.push(x),p=0;p<x.length;p++){var ot=x[p],s=U.indexOf(ot);s>-1&&U.splice(s,1)}N=new Set,P=new Map}else c=[]}return c},o.prototype.createDummyNodesForBendpoints=function(c){for(var n=[],y=c.source,v=this.graphManager.calcLowestCommonAncestor(c.source,c.target),p=0;p<c.bendpoints.length;p++){var N=this.newNode(null);N.setRect(new Point(0,0),new Dimension(1,1)),v.add(N);var _=this.newEdge(null);this.graphManager.add(_,y,N),n.add(N),y=N}return _=this.newEdge(null),this.graphManager.add(_,y,c.target),this.edgeToDummyNodes.set(c,n),c.isInterGraph()?this.graphManager.remove(c):v.remove(c),n},o.prototype.createBendpointsFromDummyNodes=function(){var c=[];c=c.concat(this.graphManager.getAllEdges()),c=[].concat(g(this.edgeToDummyNodes.keys())).concat(c);for(var n=0;n<c.length;n++){var y=c[n];if(y.bendpoints.length>0){for(var v=this.edgeToDummyNodes.get(y),p=0;p<v.length;p++){var N=v[p],_=new l(N.getCenterX(),N.getCenterY()),P=y.bendpoints.get(p);P.x=_.x,P.y=_.y,N.getOwner().remove(N)}this.graphManager.add(y,y.source,y.target)}}},o.transform=function(c,n,y,v){if(y!=null&&v!=null){var p=n;return c<=50?p-=(n-n/y)/50*(50-c):p+=(n*v-n)/50*(c-50),p}var N,_;return c<=50?(N=9*n/500,_=n/10):(N=9*n/50,_=-8*n),N*c+_},o.findCenterOfTree=function(c){var n=[];n=n.concat(c);var y=[],v=new Map,p=!1,N=null;n.length!=1&&n.length!=2||(p=!0,N=n[0]);for(var _=0;_<n.length;_++){var P=(H=n[_]).getNeighborsList().size;v.set(H,H.getNeighborsList().size),P==1&&y.push(H)}var U=[];for(U=U.concat(y);!p;){var F=[];for(F=F.concat(U),U=[],_=0;_<n.length;_++){var H=n[_],V=n.indexOf(H);V>=0&&n.splice(V,1),H.getNeighborsList().forEach(function(x){if(y.indexOf(x)<0){var ot=v.get(x)-1;ot==1&&U.push(x),v.set(x,ot)}})}y=y.concat(U),n.length!=1&&n.length!=2||(p=!0,N=n[0])}return N},o.prototype.setGraphManager=function(c){this.graphManager=c},L.exports=o},function(L,b,T){function g(){}g.seed=1,g.x=0,g.nextDouble=function(){return g.x=1e4*Math.sin(g.seed++),g.x-Math.floor(g.x)},L.exports=g},function(L,b,T){var g=T(5);function i(a,t){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}i.prototype.getWorldOrgX=function(){return this.lworldOrgX},i.prototype.setWorldOrgX=function(a){this.lworldOrgX=a},i.prototype.getWorldOrgY=function(){return this.lworldOrgY},i.prototype.setWorldOrgY=function(a){this.lworldOrgY=a},i.prototype.getWorldExtX=function(){return this.lworldExtX},i.prototype.setWorldExtX=function(a){this.lworldExtX=a},i.prototype.getWorldExtY=function(){return this.lworldExtY},i.prototype.setWorldExtY=function(a){this.lworldExtY=a},i.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},i.prototype.setDeviceOrgX=function(a){this.ldeviceOrgX=a},i.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},i.prototype.setDeviceOrgY=function(a){this.ldeviceOrgY=a},i.prototype.getDeviceExtX=function(){return this.ldeviceExtX},i.prototype.setDeviceExtX=function(a){this.ldeviceExtX=a},i.prototype.getDeviceExtY=function(){return this.ldeviceExtY},i.prototype.setDeviceExtY=function(a){this.ldeviceExtY=a},i.prototype.transformX=function(a){var t=0,r=this.lworldExtX;return r!=0&&(t=this.ldeviceOrgX+(a-this.lworldOrgX)*this.ldeviceExtX/r),t},i.prototype.transformY=function(a){var t=0,r=this.lworldExtY;return r!=0&&(t=this.ldeviceOrgY+(a-this.lworldOrgY)*this.ldeviceExtY/r),t},i.prototype.inverseTransformX=function(a){var t=0,r=this.ldeviceExtX;return r!=0&&(t=this.lworldOrgX+(a-this.ldeviceOrgX)*this.lworldExtX/r),t},i.prototype.inverseTransformY=function(a){var t=0,r=this.ldeviceExtY;return r!=0&&(t=this.lworldOrgY+(a-this.ldeviceOrgY)*this.lworldExtY/r),t},i.prototype.inverseTransformPoint=function(a){return new g(this.inverseTransformX(a.x),this.inverseTransformY(a.y))},L.exports=i},function(L,b,T){var g=T(15),i=T(4),a=T(0),t=T(8),r=T(9);function e(){g.call(this),this.useSmartIdealEdgeLengthCalculation=i.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=i.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=i.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=i.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*i.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=i.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=i.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=i.MAX_ITERATIONS}for(var l in e.prototype=Object.create(g.prototype),g)e[l]=g[l];e.prototype.initParameters=function(){g.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=i.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},e.prototype.calcIdealEdgeLengths=function(){for(var h,d,o,c,n,y,v,p=this.getGraphManager().getAllEdges(),N=0;N<p.length;N++)d=(h=p[N]).idealLength,h.isInterGraph&&(c=h.getSource(),n=h.getTarget(),y=h.getSourceInLca().getEstimatedSize(),v=h.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(h.idealLength+=y+v-2*a.SIMPLE_NODE_SIZE),o=h.getLca().getInclusionTreeDepth(),h.idealLength+=d*i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+n.getInclusionTreeDepth()-2*o))},e.prototype.initSpringEmbedder=function(){var h=this.getAllNodes().length;this.incremental?(h>i.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*i.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(h-i.ADAPTATION_LOWER_NODE_LIMIT)/(i.ADAPTATION_UPPER_NODE_LIMIT-i.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-i.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=i.MAX_NODE_DISPLACEMENT_INCREMENTAL):(h>i.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(i.COOLING_ADAPTATION_FACTOR,1-(h-i.ADAPTATION_LOWER_NODE_LIMIT)/(i.ADAPTATION_UPPER_NODE_LIMIT-i.ADAPTATION_LOWER_NODE_LIMIT)*(1-i.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=i.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.displacementThresholdPerNode=3*i.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},e.prototype.calcSpringForces=function(){for(var h,d=this.getAllEdges(),o=0;o<d.length;o++)h=d[o],this.calcSpringForce(h,h.idealLength)},e.prototype.calcRepulsionForces=function(){var h,d,o,c,n,y=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],v=arguments.length>1&&arguments[1]!==void 0&&arguments[1],p=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%i.GRID_CALCULATION_CHECK_PERIOD==1&&y&&this.updateGrid(),n=new Set,h=0;h<p.length;h++)o=p[h],this.calculateRepulsionForceOfANode(o,n,y,v),n.add(o);else for(h=0;h<p.length;h++)for(o=p[h],d=h+1;d<p.length;d++)c=p[d],o.getOwner()==c.getOwner()&&this.calcRepulsionForce(o,c)},e.prototype.calcGravitationalForces=function(){for(var h,d=this.getAllNodesToApplyGravitation(),o=0;o<d.length;o++)h=d[o],this.calcGravitationalForce(h)},e.prototype.moveNodes=function(){for(var h=this.getAllNodes(),d=0;d<h.length;d++)h[d].move()},e.prototype.calcSpringForce=function(h,d){var o,c,n,y,v=h.getSource(),p=h.getTarget();if(this.uniformLeafNodeSizes&&v.getChild()==null&&p.getChild()==null)h.updateLengthSimple();else if(h.updateLength(),h.isOverlapingSourceAndTarget)return;(o=h.getLength())!=0&&(n=(c=h.edgeElasticity*(o-d))*(h.lengthX/o),y=c*(h.lengthY/o),v.springForceX+=n,v.springForceY+=y,p.springForceX-=n,p.springForceY-=y)},e.prototype.calcRepulsionForce=function(h,d){var o,c,n,y,v,p,N,_=h.getRect(),P=d.getRect(),U=new Array(2),F=new Array(4);if(_.intersects(P)){t.calcSeparationAmount(_,P,U,i.DEFAULT_EDGE_LENGTH/2),p=2*U[0],N=2*U[1];var H=h.noOfChildren*d.noOfChildren/(h.noOfChildren+d.noOfChildren);h.repulsionForceX-=H*p,h.repulsionForceY-=H*N,d.repulsionForceX+=H*p,d.repulsionForceY+=H*N}else this.uniformLeafNodeSizes&&h.getChild()==null&&d.getChild()==null?(o=P.getCenterX()-_.getCenterX(),c=P.getCenterY()-_.getCenterY()):(t.getIntersection(_,P,F),o=F[2]-F[0],c=F[3]-F[1]),Math.abs(o)<i.MIN_REPULSION_DIST&&(o=r.sign(o)*i.MIN_REPULSION_DIST),Math.abs(c)<i.MIN_REPULSION_DIST&&(c=r.sign(c)*i.MIN_REPULSION_DIST),n=o*o+c*c,y=Math.sqrt(n),p=(v=(h.nodeRepulsion/2+d.nodeRepulsion/2)*h.noOfChildren*d.noOfChildren/n)*o/y,N=v*c/y,h.repulsionForceX-=p,h.repulsionForceY-=N,d.repulsionForceX+=p,d.repulsionForceY+=N},e.prototype.calcGravitationalForce=function(h){var d,o,c,n,y,v,p,N;o=((d=h.getOwner()).getRight()+d.getLeft())/2,c=(d.getTop()+d.getBottom())/2,n=h.getCenterX()-o,y=h.getCenterY()-c,v=Math.abs(n)+h.getWidth()/2,p=Math.abs(y)+h.getHeight()/2,h.getOwner()==this.graphManager.getRoot()?(v>(N=d.getEstimatedSize()*this.gravityRangeFactor)||p>N)&&(h.gravitationForceX=-this.gravityConstant*n,h.gravitationForceY=-this.gravityConstant*y):(v>(N=d.getEstimatedSize()*this.compoundGravityRangeFactor)||p>N)&&(h.gravitationForceX=-this.gravityConstant*n*this.compoundGravityConstant,h.gravitationForceY=-this.gravityConstant*y*this.compoundGravityConstant)},e.prototype.isConverged=function(){var h,d=!1;return this.totalIterations>this.maxIterations/3&&(d=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),h=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,h||d},e.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},e.prototype.calcNoOfChildrenForAllNodes=function(){for(var h,d=this.graphManager.getAllNodes(),o=0;o<d.length;o++)(h=d[o]).noOfChildren=h.getNoOfChildren()},e.prototype.calcGrid=function(h){var d,o;d=parseInt(Math.ceil((h.getRight()-h.getLeft())/this.repulsionRange)),o=parseInt(Math.ceil((h.getBottom()-h.getTop())/this.repulsionRange));for(var c=new Array(d),n=0;n<d;n++)c[n]=new Array(o);for(n=0;n<d;n++)for(var y=0;y<o;y++)c[n][y]=new Array;return c},e.prototype.addNodeToGrid=function(h,d,o){var c,n,y,v;c=parseInt(Math.floor((h.getRect().x-d)/this.repulsionRange)),n=parseInt(Math.floor((h.getRect().width+h.getRect().x-d)/this.repulsionRange)),y=parseInt(Math.floor((h.getRect().y-o)/this.repulsionRange)),v=parseInt(Math.floor((h.getRect().height+h.getRect().y-o)/this.repulsionRange));for(var p=c;p<=n;p++)for(var N=y;N<=v;N++)this.grid[p][N].push(h),h.setGridCoordinates(c,n,y,v)},e.prototype.updateGrid=function(){var h,d,o=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),h=0;h<o.length;h++)d=o[h],this.addNodeToGrid(d,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},e.prototype.calculateRepulsionForceOfANode=function(h,d,o,c){if(this.totalIterations%i.GRID_CALCULATION_CHECK_PERIOD==1&&o||c){var n,y=new Set;h.surrounding=new Array;for(var v=this.grid,p=h.startX-1;p<h.finishX+2;p++)for(var N=h.startY-1;N<h.finishY+2;N++)if(!(p<0||N<0||p>=v.length||N>=v[0].length)){for(var _=0;_<v[p][N].length;_++)if(n=v[p][N][_],h.getOwner()==n.getOwner()&&h!=n&&!d.has(n)&&!y.has(n)){var P=Math.abs(h.getCenterX()-n.getCenterX())-(h.getWidth()/2+n.getWidth()/2),U=Math.abs(h.getCenterY()-n.getCenterY())-(h.getHeight()/2+n.getHeight()/2);P<=this.repulsionRange&&U<=this.repulsionRange&&y.add(n)}}h.surrounding=[].concat(function(F){if(Array.isArray(F)){for(var H=0,V=Array(F.length);H<F.length;H++)V[H]=F[H];return V}return Array.from(F)}(y))}for(p=0;p<h.surrounding.length;p++)this.calcRepulsionForce(h,h.surrounding[p])},e.prototype.calcRepulsionRange=function(){return 0},L.exports=e},function(L,b,T){var g=T(1),i=T(4);function a(r,e,l){g.call(this,r,e,l),this.idealLength=i.DEFAULT_EDGE_LENGTH,this.edgeElasticity=i.DEFAULT_SPRING_STRENGTH}for(var t in a.prototype=Object.create(g.prototype),g)a[t]=g[t];L.exports=a},function(L,b,T){var g=T(3),i=T(4);function a(r,e,l,h){g.call(this,r,e,l,h),this.nodeRepulsion=i.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var t in a.prototype=Object.create(g.prototype),g)a[t]=g[t];a.prototype.setGridCoordinates=function(r,e,l,h){this.startX=r,this.finishX=e,this.startY=l,this.finishY=h},L.exports=a},function(L,b,T){function g(i,a){this.width=0,this.height=0,i!==null&&a!==null&&(this.height=a,this.width=i)}g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(i){this.width=i},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(i){this.height=i},L.exports=g},function(L,b,T){var g=T(14);function i(){this.map={},this.keys=[]}i.prototype.put=function(a,t){var r=g.createID(a);this.contains(r)||(this.map[r]=t,this.keys.push(a))},i.prototype.contains=function(a){return g.createID(a),this.map[a]!=null},i.prototype.get=function(a){var t=g.createID(a);return this.map[t]},i.prototype.keySet=function(){return this.keys},L.exports=i},function(L,b,T){var g=T(14);function i(){this.set={}}i.prototype.add=function(a){var t=g.createID(a);this.contains(t)||(this.set[t]=a)},i.prototype.remove=function(a){delete this.set[g.createID(a)]},i.prototype.clear=function(){this.set={}},i.prototype.contains=function(a){return this.set[g.createID(a)]==a},i.prototype.isEmpty=function(){return this.size()===0},i.prototype.size=function(){return Object.keys(this.set).length},i.prototype.addAllTo=function(a){for(var t=Object.keys(this.set),r=t.length,e=0;e<r;e++)a.push(this.set[t[e]])},i.prototype.size=function(){return Object.keys(this.set).length},i.prototype.addAll=function(a){for(var t=a.length,r=0;r<t;r++){var e=a[r];this.add(e)}},L.exports=i},function(L,b,T){function g(){}g.multMat=function(i,a){for(var t=[],r=0;r<i.length;r++){t[r]=[];for(var e=0;e<a[0].length;e++){t[r][e]=0;for(var l=0;l<i[0].length;l++)t[r][e]+=i[r][l]*a[l][e]}}return t},g.transpose=function(i){for(var a=[],t=0;t<i[0].length;t++){a[t]=[];for(var r=0;r<i.length;r++)a[t][r]=i[r][t]}return a},g.multCons=function(i,a){for(var t=[],r=0;r<i.length;r++)t[r]=i[r]*a;return t},g.minusOp=function(i,a){for(var t=[],r=0;r<i.length;r++)t[r]=i[r]-a[r];return t},g.dotProduct=function(i,a){for(var t=0,r=0;r<i.length;r++)t+=i[r]*a[r];return t},g.mag=function(i){return Math.sqrt(this.dotProduct(i,i))},g.normalize=function(i){for(var a=[],t=this.mag(i),r=0;r<i.length;r++)a[r]=i[r]/t;return a},g.multGamma=function(i){for(var a=[],t=0,r=0;r<i.length;r++)t+=i[r];t*=-1/i.length;for(var e=0;e<i.length;e++)a[e]=t+i[e];return a},g.multL=function(i,a,t){for(var r=[],e=[],l=[],h=0;h<a[0].length;h++){for(var d=0,o=0;o<a.length;o++)d+=-.5*a[o][h]*i[o];e[h]=d}for(var c=0;c<t.length;c++){for(var n=0,y=0;y<t.length;y++)n+=t[c][y]*e[y];l[c]=n}for(var v=0;v<a.length;v++){for(var p=0,N=0;N<a[0].length;N++)p+=a[v][N]*l[N];r[v]=p}return r},L.exports=g},function(L,b,T){var g=function(){function t(r,e){for(var l=0;l<e.length;l++){var h=e[l];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(r,h.key,h)}}return function(r,e,l){return e&&t(r.prototype,e),l&&t(r,l),r}}(),i=T(11),a=function(){function t(r,e){(function(h,d){if(!(h instanceof d))throw new TypeError("Cannot call a class as a function")})(this,t),e===null&&e===void 0||(this.compareFunction=this._defaultCompareFunction);var l=void 0;l=r instanceof i?r.size():r.length,this._quicksort(r,0,l-1)}return g(t,[{key:"_quicksort",value:function(r,e,l){if(e<l){var h=this._partition(r,e,l);this._quicksort(r,e,h),this._quicksort(r,h+1,l)}}},{key:"_partition",value:function(r,e,l){for(var h=this._get(r,e),d=e,o=l;;){for(;this.compareFunction(h,this._get(r,o));)o--;for(;this.compareFunction(this._get(r,d),h);)d++;if(!(d<o))return o;this._swap(r,d,o),d++,o--}}},{key:"_get",value:function(r,e){return r instanceof i?r.get_object_at(e):r[e]}},{key:"_set",value:function(r,e,l){r instanceof i?r.set_object_at(e,l):r[e]=l}},{key:"_swap",value:function(r,e,l){var h=this._get(r,e);this._set(r,e,this._get(r,l)),this._set(r,l,h)}},{key:"_defaultCompareFunction",value:function(r,e){return e>r}}]),t}();L.exports=a},function(L,b,T){function g(){}g.svd=function(i){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=i.length,this.n=i[0].length;var a=Math.min(this.m,this.n);this.s=function(_t){for(var lt=[];_t-- >0;)lt.push(0);return lt}(Math.min(this.m+1,this.n)),this.U=function _t(lt){if(lt.length==0)return 0;for(var Ft=[],Ht=0;Ht<lt[0];Ht++)Ft.push(_t(lt.slice(1)));return Ft}([this.m,a]),this.V=function(_t){return function lt(Ft){if(Ft.length==0)return 0;for(var Ht=[],$t=0;$t<Ft[0];$t++)Ht.push(lt(Ft.slice(1)));return Ht}(_t)}([this.n,this.n]);for(var t,r,e=function(_t){for(var lt=[];_t-- >0;)lt.push(0);return lt}(this.n),l=function(_t){for(var lt=[];_t-- >0;)lt.push(0);return lt}(this.m),h=Math.min(this.m-1,this.n),d=Math.max(0,Math.min(this.n-2,this.m)),o=0;o<Math.max(h,d);o++){if(o<h){this.s[o]=0;for(var c=o;c<this.m;c++)this.s[o]=g.hypot(this.s[o],i[c][o]);if(this.s[o]!==0){i[o][o]<0&&(this.s[o]=-this.s[o]);for(var n=o;n<this.m;n++)i[n][o]/=this.s[o];i[o][o]+=1}this.s[o]=-this.s[o]}for(var y=o+1;y<this.n;y++){if(t=o<h,r=this.s[o]!==0,t&&r){for(var v=0,p=o;p<this.m;p++)v+=i[p][o]*i[p][y];v=-v/i[o][o];for(var N=o;N<this.m;N++)i[N][y]+=v*i[N][o]}e[y]=i[o][y]}if(function(_t,lt){return lt}(0,o<h))for(var _=o;_<this.m;_++)this.U[_][o]=i[_][o];if(o<d){e[o]=0;for(var P=o+1;P<this.n;P++)e[o]=g.hypot(e[o],e[P]);if(e[o]!==0){e[o+1]<0&&(e[o]=-e[o]);for(var U=o+1;U<this.n;U++)e[U]/=e[o];e[o+1]+=1}if(e[o]=-e[o],function(_t,lt){return _t&&lt}(o+1<this.m,e[o]!==0)){for(var F=o+1;F<this.m;F++)l[F]=0;for(var H=o+1;H<this.n;H++)for(var V=o+1;V<this.m;V++)l[V]+=e[H]*i[V][H];for(var x=o+1;x<this.n;x++)for(var ot=-e[x]/e[o+1],s=o+1;s<this.m;s++)i[s][x]+=ot*l[s]}for(var E=o+1;E<this.n;E++)this.V[E][o]=e[E]}}var u=Math.min(this.n,this.m+1);h<this.n&&(this.s[h]=i[h][h]),this.m<u&&(this.s[u-1]=0),d+1<u&&(e[d]=i[d][u-1]),e[u-1]=0;for(var m=h;m<a;m++){for(var f=0;f<this.m;f++)this.U[f][m]=0;this.U[m][m]=1}for(var w=h-1;w>=0;w--)if(this.s[w]!==0){for(var C=w+1;C<a;C++){for(var G=0,R=w;R<this.m;R++)G+=this.U[R][w]*this.U[R][C];G=-G/this.U[w][w];for(var M=w;M<this.m;M++)this.U[M][C]+=G*this.U[M][w]}for(var X=w;X<this.m;X++)this.U[X][w]=-this.U[X][w];this.U[w][w]=1+this.U[w][w];for(var S=0;S<w-1;S++)this.U[S][w]=0}else{for(var B=0;B<this.m;B++)this.U[B][w]=0;this.U[w][w]=1}for(var W=this.n-1;W>=0;W--){if(function(_t,lt){return _t&&lt}(W<d,e[W]!==0))for(var D=W+1;D<a;D++){for(var I=0,k=W+1;k<this.n;k++)I+=this.V[k][W]*this.V[k][D];I=-I/this.V[W+1][W];for(var z=W+1;z<this.n;z++)this.V[z][D]+=I*this.V[z][W]}for(var Q=0;Q<this.n;Q++)this.V[Q][W]=0;this.V[W][W]=1}for(var J=u-1,at=Math.pow(2,-52),mt=Math.pow(2,-966);u>0;){var $=void 0,Gt=void 0;for($=u-2;$>=-1&&$!==-1;$--)if(Math.abs(e[$])<=mt+at*(Math.abs(this.s[$])+Math.abs(this.s[$+1]))){e[$]=0;break}if($===u-2)Gt=4;else{var ft=void 0;for(ft=u-1;ft>=$&&ft!==$;ft--){var xt=(ft!==u?Math.abs(e[ft]):0)+(ft!==$+1?Math.abs(e[ft-1]):0);if(Math.abs(this.s[ft])<=mt+at*xt){this.s[ft]=0;break}}ft===$?Gt=3:ft===u-1?Gt=1:(Gt=2,$=ft)}switch($++,Gt){case 1:var Yt=e[u-2];e[u-2]=0;for(var Mt=u-2;Mt>=$;Mt--){var kt=g.hypot(this.s[Mt],Yt),ct=this.s[Mt]/kt,ht=Yt/kt;this.s[Mt]=kt,Mt!==$&&(Yt=-ht*e[Mt-1],e[Mt-1]=ct*e[Mt-1]);for(var st=0;st<this.n;st++)kt=ct*this.V[st][Mt]+ht*this.V[st][u-1],this.V[st][u-1]=-ht*this.V[st][Mt]+ct*this.V[st][u-1],this.V[st][Mt]=kt}break;case 2:var yt=e[$-1];e[$-1]=0;for(var vt=$;vt<u;vt++){var Et=g.hypot(this.s[vt],yt),Nt=this.s[vt]/Et,Lt=yt/Et;this.s[vt]=Et,yt=-Lt*e[vt],e[vt]=Nt*e[vt];for(var O=0;O<this.m;O++)Et=Nt*this.U[O][vt]+Lt*this.U[O][$-1],this.U[O][$-1]=-Lt*this.U[O][vt]+Nt*this.U[O][$-1],this.U[O][vt]=Et}break;case 3:var Y=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[u-1]),Math.abs(this.s[u-2])),Math.abs(e[u-2])),Math.abs(this.s[$])),Math.abs(e[$])),Z=this.s[u-1]/Y,j=this.s[u-2]/Y,tt=e[u-2]/Y,ut=this.s[$]/Y,rt=e[$]/Y,pt=((j+Z)*(j-Z)+tt*tt)/2,At=Z*tt*(Z*tt),Ut=0;(function(_t,lt){return _t||lt})(pt!==0,At!==0)&&(Ut=Math.sqrt(pt*pt+At),pt<0&&(Ut=-Ut),Ut=At/(pt+Ut));for(var Ot=(ut+Z)*(ut-Z)+Ut,wt=ut*rt,q=$;q<u-1;q++){var K=g.hypot(Ot,wt),gt=Ot/K,Tt=wt/K;q!==$&&(e[q-1]=K),Ot=gt*this.s[q]+Tt*e[q],e[q]=gt*e[q]-Tt*this.s[q],wt=Tt*this.s[q+1],this.s[q+1]=gt*this.s[q+1];for(var nt=0;nt<this.n;nt++)K=gt*this.V[nt][q]+Tt*this.V[nt][q+1],this.V[nt][q+1]=-Tt*this.V[nt][q]+gt*this.V[nt][q+1],this.V[nt][q]=K;if(gt=Ot/(K=g.hypot(Ot,wt)),Tt=wt/K,this.s[q]=K,Ot=gt*e[q]+Tt*this.s[q+1],this.s[q+1]=-Tt*e[q]+gt*this.s[q+1],wt=Tt*e[q+1],e[q+1]=gt*e[q+1],q<this.m-1)for(var Ct=0;Ct<this.m;Ct++)K=gt*this.U[Ct][q]+Tt*this.U[Ct][q+1],this.U[Ct][q+1]=-Tt*this.U[Ct][q]+gt*this.U[Ct][q+1],this.U[Ct][q]=K}e[u-2]=Ot;break;case 4:if(this.s[$]<=0){this.s[$]=this.s[$]<0?-this.s[$]:0;for(var St=0;St<=J;St++)this.V[St][$]=-this.V[St][$]}for(;$<J&&!(this.s[$]>=this.s[$+1]);){var Dt=this.s[$];if(this.s[$]=this.s[$+1],this.s[$+1]=Dt,$<this.n-1)for(var Pt=0;Pt<this.n;Pt++)Dt=this.V[Pt][$+1],this.V[Pt][$+1]=this.V[Pt][$],this.V[Pt][$]=Dt;if($<this.m-1)for(var Rt=0;Rt<this.m;Rt++)Dt=this.U[Rt][$+1],this.U[Rt][$+1]=this.U[Rt][$],this.U[Rt][$]=Dt;$++}u--}}return{U:this.U,V:this.V,S:this.s}},g.hypot=function(i,a){var t=void 0;return Math.abs(i)>Math.abs(a)?(t=a/i,t=Math.abs(i)*Math.sqrt(1+t*t)):a!=0?(t=i/a,t=Math.abs(a)*Math.sqrt(1+t*t)):t=0,t},L.exports=g},function(L,b,T){var g=function(){function a(t,r){for(var e=0;e<r.length;e++){var l=r[e];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,l.key,l)}}return function(t,r,e){return r&&a(t.prototype,r),e&&a(t,e),t}}(),i=function(){function a(t,r){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,l=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,h=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;(function(y,v){if(!(y instanceof v))throw new TypeError("Cannot call a class as a function")})(this,a),this.sequence1=t,this.sequence2=r,this.match_score=e,this.mismatch_penalty=l,this.gap_penalty=h,this.iMax=t.length+1,this.jMax=r.length+1,this.grid=new Array(this.iMax);for(var d=0;d<this.iMax;d++){this.grid[d]=new Array(this.jMax);for(var o=0;o<this.jMax;o++)this.grid[d][o]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var n=0;n<this.jMax;n++)this.tracebackGrid[c][n]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return g(a,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var t=1;t<this.jMax;t++)this.grid[0][t]=this.grid[0][t-1]+this.gap_penalty,this.tracebackGrid[0][t]=[!1,!1,!0];for(var r=1;r<this.iMax;r++)this.grid[r][0]=this.grid[r-1][0]+this.gap_penalty,this.tracebackGrid[r][0]=[!1,!0,!1];for(var e=1;e<this.iMax;e++)for(var l=1;l<this.jMax;l++){var h=[this.sequence1[e-1]===this.sequence2[l-1]?this.grid[e-1][l-1]+this.match_score:this.grid[e-1][l-1]+this.mismatch_penalty,this.grid[e-1][l]+this.gap_penalty,this.grid[e][l-1]+this.gap_penalty],d=this.arrayAllMaxIndexes(h);this.grid[e][l]=h[d[0]],this.tracebackGrid[e][l]=[d.includes(0),d.includes(1),d.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var t=[];for(t.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});t[0];){var r=t[0],e=this.tracebackGrid[r.pos[0]][r.pos[1]];e[0]&&t.push({pos:[r.pos[0]-1,r.pos[1]-1],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2}),e[1]&&t.push({pos:[r.pos[0]-1,r.pos[1]],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:"-"+r.seq2}),e[2]&&t.push({pos:[r.pos[0],r.pos[1]-1],seq1:"-"+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2}),r.pos[0]===0&&r.pos[1]===0&&this.alignments.push({sequence1:r.seq1,sequence2:r.seq2}),t.shift()}return this.alignments}},{key:"getAllIndexes",value:function(t,r){for(var e=[],l=-1;(l=t.indexOf(r,l+1))!==-1;)e.push(l);return e}},{key:"arrayAllMaxIndexes",value:function(t){return this.getAllIndexes(t,Math.max.apply(null,t))}}]),a}();L.exports=i},function(L,b,T){var g=function(){};g.FDLayout=T(18),g.FDLayoutConstants=T(4),g.FDLayoutEdge=T(19),g.FDLayoutNode=T(20),g.DimensionD=T(21),g.HashMap=T(22),g.HashSet=T(23),g.IGeometry=T(8),g.IMath=T(9),g.Integer=T(10),g.Point=T(12),g.PointD=T(5),g.RandomSeed=T(16),g.RectangleD=T(13),g.Transform=T(17),g.UniqueIDGeneretor=T(14),g.Quicksort=T(25),g.LinkedList=T(11),g.LGraphObject=T(2),g.LGraph=T(6),g.LEdge=T(1),g.LGraphManager=T(7),g.LNode=T(3),g.Layout=T(15),g.LayoutConstants=T(0),g.NeedlemanWunsch=T(27),g.Matrix=T(24),g.SVD=T(26),L.exports=g},function(L,b,T){function g(){this.listeners=[]}var i=g.prototype;i.addListener=function(a,t){this.listeners.push({event:a,callback:t})},i.removeListener=function(a,t){for(var r=this.listeners.length;r>=0;r--){var e=this.listeners[r];e.event===a&&e.callback===t&&this.listeners.splice(r,1)}},i.emit=function(a,t){for(var r=0;r<this.listeners.length;r++){var e=this.listeners[r];a===e.event&&e.callback(t)}},L.exports=g}])},Te.exports=A()),Te.exports;var A}function ir(){return Ee?Ne.exports:(Ee=1,A=function(L){return b={45:(i,a,t)=>{var r={};r.layoutBase=t(551),r.CoSEConstants=t(806),r.CoSEEdge=t(767),r.CoSEGraph=t(880),r.CoSEGraphManager=t(578),r.CoSELayout=t(765),r.CoSENode=t(991),r.ConstraintHandler=t(902),i.exports=r},806:(i,a,t)=>{var r=t(551).FDLayoutConstants;function e(){}for(var l in r)e[l]=r[l];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=r.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,e.ENFORCE_CONSTRAINTS=!0,e.APPLY_LAYOUT=!0,e.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,e.TREE_REDUCTION_ON_INCREMENTAL=!0,e.PURE_INCREMENTAL=e.DEFAULT_INCREMENTAL,i.exports=e},767:(i,a,t)=>{var r=t(551).FDLayoutEdge;function e(h,d,o){r.call(this,h,d,o)}for(var l in e.prototype=Object.create(r.prototype),r)e[l]=r[l];i.exports=e},880:(i,a,t)=>{var r=t(551).LGraph;function e(h,d,o){r.call(this,h,d,o)}for(var l in e.prototype=Object.create(r.prototype),r)e[l]=r[l];i.exports=e},578:(i,a,t)=>{var r=t(551).LGraphManager;function e(h){r.call(this,h)}for(var l in e.prototype=Object.create(r.prototype),r)e[l]=r[l];i.exports=e},765:(i,a,t)=>{var r=t(551).FDLayout,e=t(578),l=t(880),h=t(991),d=t(767),o=t(806),c=t(902),n=t(551).FDLayoutConstants,y=t(551).LayoutConstants,v=t(551).Point,p=t(551).PointD,N=t(551).DimensionD,_=t(551).Layout,P=t(551).Integer,U=t(551).IGeometry,F=t(551).LGraph,H=t(551).Transform,V=t(551).LinkedList;function x(){r.call(this),this.toBeTiled={},this.constraints={}}for(var ot in x.prototype=Object.create(r.prototype),r)x[ot]=r[ot];x.prototype.newGraphManager=function(){var s=new e(this);return this.graphManager=s,s},x.prototype.newGraph=function(s){return new l(null,this.graphManager,s)},x.prototype.newNode=function(s){return new h(this.graphManager,s)},x.prototype.newEdge=function(s){return new d(null,null,s)},x.prototype.initParameters=function(){r.prototype.initParameters.call(this,arguments),this.isSubLayout||(o.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=o.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=n.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=n.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=n.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},x.prototype.initSpringEmbedder=function(){r.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/n.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},x.prototype.layout=function(){return y.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},x.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental)o.TREE_REDUCTION_ON_INCREMENTAL&&(this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation(),E=new Set(this.getAllNodes()),u=this.nodesWithGravity.filter(function(m){return E.has(m)}),this.graphManager.setAllNodesToApplyGravitation(u));else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var E=new Set(this.getAllNodes()),u=this.nodesWithGravity.filter(function(m){return E.has(m)});this.graphManager.setAllNodesToApplyGravitation(u),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),o.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},x.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}if(this.totalIterations%n.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),E=this.nodesWithGravity.filter(function(f){return s.has(f)});this.graphManager.setAllNodesToApplyGravitation(E),this.graphManager.updateBounds(),this.updateGrid(),o.PURE_INCREMENTAL?this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),o.PURE_INCREMENTAL?this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var u=!this.isTreeGrowing&&!this.isGrowthFinished,m=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(u,m),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},x.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),E={},u=0;u<s.length;u++){var m=s[u].rect,f=s[u].id;E[f]={id:f,x:m.getCenterX(),y:m.getCenterY(),w:m.width,h:m.height}}return E},x.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(n.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},x.prototype.moveNodes=function(){for(var s=this.getAllNodes(),E=0;E<s.length;E++)s[E].calculateDisplacement();for(Object.keys(this.constraints).length>0&&this.updateDisplacements(),E=0;E<s.length;E++)s[E].move()},x.prototype.initConstraintVariables=function(){var s=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var E=this.graphManager.getAllNodes(),u=0;u<E.length;u++){var m=E[u];this.idToNodeMap.set(m.id,m)}var f=function I(k){for(var z,Q=k.getChild().getNodes(),J=0,at=0;at<Q.length;at++)(z=Q[at]).getChild()==null?s.fixedNodeSet.has(z.id)&&(J+=100):J+=I(z);return J};if(this.constraints.fixedNodeConstraint){for(this.constraints.fixedNodeConstraint.forEach(function(I){s.fixedNodeSet.add(I.nodeId)}),E=this.graphManager.getAllNodes(),u=0;u<E.length;u++)if((m=E[u]).getChild()!=null){var w=f(m);w>0&&(m.fixedNodeWeight=w)}}if(this.constraints.relativePlacementConstraint){var C=new Map,G=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(I){s.fixedNodesOnHorizontal.add(I),s.fixedNodesOnVertical.add(I)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical){var R=this.constraints.alignmentConstraint.vertical;for(u=0;u<R.length;u++)this.dummyToNodeForVerticalAlignment.set("dummy"+u,[]),R[u].forEach(function(I){C.set(I,"dummy"+u),s.dummyToNodeForVerticalAlignment.get("dummy"+u).push(I),s.fixedNodeSet.has(I)&&s.fixedNodesOnHorizontal.add("dummy"+u)})}if(this.constraints.alignmentConstraint.horizontal){var M=this.constraints.alignmentConstraint.horizontal;for(u=0;u<M.length;u++)this.dummyToNodeForHorizontalAlignment.set("dummy"+u,[]),M[u].forEach(function(I){G.set(I,"dummy"+u),s.dummyToNodeForHorizontalAlignment.get("dummy"+u).push(I),s.fixedNodeSet.has(I)&&s.fixedNodesOnVertical.add("dummy"+u)})}}if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(I){var k,z,Q;for(Q=I.length-1;Q>=2*I.length/3;Q--)k=Math.floor(Math.random()*(Q+1)),z=I[Q],I[Q]=I[k],I[k]=z;return I},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(I){if(I.left){var k=C.has(I.left)?C.get(I.left):I.left,z=C.has(I.right)?C.get(I.right):I.right;s.nodesInRelativeHorizontal.includes(k)||(s.nodesInRelativeHorizontal.push(k),s.nodeToRelativeConstraintMapHorizontal.set(k,[]),s.dummyToNodeForVerticalAlignment.has(k)?s.nodeToTempPositionMapHorizontal.set(k,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(k)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(k,s.idToNodeMap.get(k).getCenterX())),s.nodesInRelativeHorizontal.includes(z)||(s.nodesInRelativeHorizontal.push(z),s.nodeToRelativeConstraintMapHorizontal.set(z,[]),s.dummyToNodeForVerticalAlignment.has(z)?s.nodeToTempPositionMapHorizontal.set(z,s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(z)[0]).getCenterX()):s.nodeToTempPositionMapHorizontal.set(z,s.idToNodeMap.get(z).getCenterX())),s.nodeToRelativeConstraintMapHorizontal.get(k).push({right:z,gap:I.gap}),s.nodeToRelativeConstraintMapHorizontal.get(z).push({left:k,gap:I.gap})}else{var Q=G.has(I.top)?G.get(I.top):I.top,J=G.has(I.bottom)?G.get(I.bottom):I.bottom;s.nodesInRelativeVertical.includes(Q)||(s.nodesInRelativeVertical.push(Q),s.nodeToRelativeConstraintMapVertical.set(Q,[]),s.dummyToNodeForHorizontalAlignment.has(Q)?s.nodeToTempPositionMapVertical.set(Q,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(Q)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(Q,s.idToNodeMap.get(Q).getCenterY())),s.nodesInRelativeVertical.includes(J)||(s.nodesInRelativeVertical.push(J),s.nodeToRelativeConstraintMapVertical.set(J,[]),s.dummyToNodeForHorizontalAlignment.has(J)?s.nodeToTempPositionMapVertical.set(J,s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(J)[0]).getCenterY()):s.nodeToTempPositionMapVertical.set(J,s.idToNodeMap.get(J).getCenterY())),s.nodeToRelativeConstraintMapVertical.get(Q).push({bottom:J,gap:I.gap}),s.nodeToRelativeConstraintMapVertical.get(J).push({top:Q,gap:I.gap})}});else{var X=new Map,S=new Map;this.constraints.relativePlacementConstraint.forEach(function(I){if(I.left){var k=C.has(I.left)?C.get(I.left):I.left,z=C.has(I.right)?C.get(I.right):I.right;X.has(k)?X.get(k).push(z):X.set(k,[z]),X.has(z)?X.get(z).push(k):X.set(z,[k])}else{var Q=G.has(I.top)?G.get(I.top):I.top,J=G.has(I.bottom)?G.get(I.bottom):I.bottom;S.has(Q)?S.get(Q).push(J):S.set(Q,[J]),S.has(J)?S.get(J).push(Q):S.set(J,[Q])}});var B=function(I,k){var z=[],Q=[],J=new V,at=new Set,mt=0;return I.forEach(function($,Gt){if(!at.has(Gt)){z[mt]=[],Q[mt]=!1;var ft=Gt;for(J.push(ft),at.add(ft),z[mt].push(ft);J.length!=0;)ft=J.shift(),k.has(ft)&&(Q[mt]=!0),I.get(ft).forEach(function(xt){at.has(xt)||(J.push(xt),at.add(xt),z[mt].push(xt))});mt++}}),{components:z,isFixed:Q}},W=B(X,s.fixedNodesOnHorizontal);this.componentsOnHorizontal=W.components,this.fixedComponentsOnHorizontal=W.isFixed;var D=B(S,s.fixedNodesOnVertical);this.componentsOnVertical=D.components,this.fixedComponentsOnVertical=D.isFixed}}},x.prototype.updateDisplacements=function(){var s=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(D){var I=s.idToNodeMap.get(D.nodeId);I.displacementX=0,I.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var E=this.constraints.alignmentConstraint.vertical,u=0;u<E.length;u++){for(var m=0,f=0;f<E[u].length;f++){if(this.fixedNodeSet.has(E[u][f])){m=0;break}m+=this.idToNodeMap.get(E[u][f]).displacementX}var w=m/E[u].length;for(f=0;f<E[u].length;f++)this.idToNodeMap.get(E[u][f]).displacementX=w}if(this.constraints.alignmentConstraint.horizontal){var C=this.constraints.alignmentConstraint.horizontal;for(u=0;u<C.length;u++){var G=0;for(f=0;f<C[u].length;f++){if(this.fixedNodeSet.has(C[u][f])){G=0;break}G+=this.idToNodeMap.get(C[u][f]).displacementY}var R=G/C[u].length;for(f=0;f<C[u].length;f++)this.idToNodeMap.get(C[u][f]).displacementY=R}}}if(this.constraints.relativePlacementConstraint)if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(D){if(!s.fixedNodesOnHorizontal.has(D)){var I=0;I=s.dummyToNodeForVerticalAlignment.has(D)?s.idToNodeMap.get(s.dummyToNodeForVerticalAlignment.get(D)[0]).displacementX:s.idToNodeMap.get(D).displacementX,s.nodeToRelativeConstraintMapHorizontal.get(D).forEach(function(k){var z;k.right?(z=s.nodeToTempPositionMapHorizontal.get(k.right)-s.nodeToTempPositionMapHorizontal.get(D)-I)<k.gap&&(I-=k.gap-z):(z=s.nodeToTempPositionMapHorizontal.get(D)-s.nodeToTempPositionMapHorizontal.get(k.left)+I)<k.gap&&(I+=k.gap-z)}),s.nodeToTempPositionMapHorizontal.set(D,s.nodeToTempPositionMapHorizontal.get(D)+I),s.dummyToNodeForVerticalAlignment.has(D)?s.dummyToNodeForVerticalAlignment.get(D).forEach(function(k){s.idToNodeMap.get(k).displacementX=I}):s.idToNodeMap.get(D).displacementX=I}}),this.nodesInRelativeVertical.forEach(function(D){if(!s.fixedNodesOnHorizontal.has(D)){var I=0;I=s.dummyToNodeForHorizontalAlignment.has(D)?s.idToNodeMap.get(s.dummyToNodeForHorizontalAlignment.get(D)[0]).displacementY:s.idToNodeMap.get(D).displacementY,s.nodeToRelativeConstraintMapVertical.get(D).forEach(function(k){var z;k.bottom?(z=s.nodeToTempPositionMapVertical.get(k.bottom)-s.nodeToTempPositionMapVertical.get(D)-I)<k.gap&&(I-=k.gap-z):(z=s.nodeToTempPositionMapVertical.get(D)-s.nodeToTempPositionMapVertical.get(k.top)+I)<k.gap&&(I+=k.gap-z)}),s.nodeToTempPositionMapVertical.set(D,s.nodeToTempPositionMapVertical.get(D)+I),s.dummyToNodeForHorizontalAlignment.has(D)?s.dummyToNodeForHorizontalAlignment.get(D).forEach(function(k){s.idToNodeMap.get(k).displacementY=I}):s.idToNodeMap.get(D).displacementY=I}});else{for(u=0;u<this.componentsOnHorizontal.length;u++){var M=this.componentsOnHorizontal[u];if(this.fixedComponentsOnHorizontal[u])for(f=0;f<M.length;f++)this.dummyToNodeForVerticalAlignment.has(M[f])?this.dummyToNodeForVerticalAlignment.get(M[f]).forEach(function(D){s.idToNodeMap.get(D).displacementX=0}):this.idToNodeMap.get(M[f]).displacementX=0;else{var X=0,S=0;for(f=0;f<M.length;f++)this.dummyToNodeForVerticalAlignment.has(M[f])?(X+=(W=this.dummyToNodeForVerticalAlignment.get(M[f])).length*this.idToNodeMap.get(W[0]).displacementX,S+=W.length):(X+=this.idToNodeMap.get(M[f]).displacementX,S++);var B=X/S;for(f=0;f<M.length;f++)this.dummyToNodeForVerticalAlignment.has(M[f])?this.dummyToNodeForVerticalAlignment.get(M[f]).forEach(function(D){s.idToNodeMap.get(D).displacementX=B}):this.idToNodeMap.get(M[f]).displacementX=B}}for(u=0;u<this.componentsOnVertical.length;u++)if(M=this.componentsOnVertical[u],this.fixedComponentsOnVertical[u])for(f=0;f<M.length;f++)this.dummyToNodeForHorizontalAlignment.has(M[f])?this.dummyToNodeForHorizontalAlignment.get(M[f]).forEach(function(D){s.idToNodeMap.get(D).displacementY=0}):this.idToNodeMap.get(M[f]).displacementY=0;else{for(X=0,S=0,f=0;f<M.length;f++){var W;this.dummyToNodeForHorizontalAlignment.has(M[f])?(X+=(W=this.dummyToNodeForHorizontalAlignment.get(M[f])).length*this.idToNodeMap.get(W[0]).displacementY,S+=W.length):(X+=this.idToNodeMap.get(M[f]).displacementY,S++)}for(B=X/S,f=0;f<M.length;f++)this.dummyToNodeForHorizontalAlignment.has(M[f])?this.dummyToNodeForHorizontalAlignment.get(M[f]).forEach(function(D){s.idToNodeMap.get(D).displacementY=B}):this.idToNodeMap.get(M[f]).displacementY=B}}},x.prototype.calculateNodesToApplyGravitationTo=function(){var s,E,u=[],m=this.graphManager.getGraphs(),f=m.length;for(E=0;E<f;E++)(s=m[E]).updateConnected(),s.isConnected||(u=u.concat(s.getNodes()));return u},x.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var E,u=new Set;for(E=0;E<s.length;E++){var m=s[E];if(!u.has(m)){var f=m.getSource(),w=m.getTarget();if(f==w)m.getBendpoints().push(new p),m.getBendpoints().push(new p),this.createDummyNodesForBendpoints(m),u.add(m);else{var C=[];if(C=(C=C.concat(f.getEdgeListToNode(w))).concat(w.getEdgeListToNode(f)),!u.has(C[0])){var G;if(C.length>1)for(G=0;G<C.length;G++){var R=C[G];R.getBendpoints().push(new p),this.createDummyNodesForBendpoints(R)}C.forEach(function(M){u.add(M)})}}}if(u.size==s.length)break}},x.prototype.positionNodesRadially=function(s){for(var E=new v(0,0),u=Math.ceil(Math.sqrt(s.length)),m=0,f=0,w=0,C=new p(0,0),G=0;G<s.length;G++){G%u==0&&(w=0,f=m,G!=0&&(f+=o.DEFAULT_COMPONENT_SEPERATION),m=0);var R=s[G],M=_.findCenterOfTree(R);E.x=w,E.y=f,(C=x.radialLayout(R,M,E)).y>m&&(m=Math.floor(C.y)),w=Math.floor(C.x+o.DEFAULT_COMPONENT_SEPERATION)}this.transform(new p(y.WORLD_CENTER_X-C.x/2,y.WORLD_CENTER_Y-C.y/2))},x.radialLayout=function(s,E,u){var m=Math.max(this.maxDiagonalInTree(s),o.DEFAULT_RADIAL_SEPARATION);x.branchRadialLayout(E,null,0,359,0,m);var f=F.calculateBounds(s),w=new H;w.setDeviceOrgX(f.getMinX()),w.setDeviceOrgY(f.getMinY()),w.setWorldOrgX(u.x),w.setWorldOrgY(u.y);for(var C=0;C<s.length;C++)s[C].transform(w);var G=new p(f.getMaxX(),f.getMaxY());return w.inverseTransformPoint(G)},x.branchRadialLayout=function(s,E,u,m,f,w){var C=(m-u+1)/2;C<0&&(C+=180);var G=(C+u)%360*U.TWO_PI/360,R=f*Math.cos(G),M=f*Math.sin(G);s.setCenter(R,M);var X=[],S=(X=X.concat(s.getEdges())).length;E!=null&&S--;for(var B,W=0,D=X.length,I=s.getEdgesBetween(E);I.length>1;){var k=I[0];I.splice(0,1);var z=X.indexOf(k);z>=0&&X.splice(z,1),D--,S--}B=E!=null?(X.indexOf(I[0])+1)%D:0;for(var Q=Math.abs(m-u)/S,J=B;W!=S;J=++J%D){var at=X[J].getOtherEnd(s);if(at!=E){var mt=(u+W*Q)%360,$=(mt+Q)%360;x.branchRadialLayout(at,s,mt,$,f+w,w),W++}}},x.maxDiagonalInTree=function(s){for(var E=P.MIN_VALUE,u=0;u<s.length;u++){var m=s[u].getDiagonal();m>E&&(E=m)}return E},x.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},x.prototype.groupZeroDegreeMembers=function(){var s=this,E={};this.memberGroups={},this.idToDummyNode={};for(var u=[],m=this.graphManager.getAllNodes(),f=0;f<m.length;f++){var w=(C=m[f]).getParent();this.getNodeDegreeWithChildren(C)!==0||w.id!=null&&this.getToBeTiled(w)||u.push(C)}for(f=0;f<u.length;f++){var C,G=(C=u[f]).getParent().id;E[G]===void 0&&(E[G]=[]),E[G]=E[G].concat(C)}Object.keys(E).forEach(function(R){if(E[R].length>1){var M="DummyCompound_"+R;s.memberGroups[M]=E[R];var X=E[R][0].getParent(),S=new h(s.graphManager);S.id=M,S.paddingLeft=X.paddingLeft||0,S.paddingRight=X.paddingRight||0,S.paddingBottom=X.paddingBottom||0,S.paddingTop=X.paddingTop||0,s.idToDummyNode[M]=S;var B=s.getGraphManager().add(s.newGraph(),S),W=X.getChild();W.add(S);for(var D=0;D<E[R].length;D++){var I=E[R][D];W.remove(I),B.add(I)}}})},x.prototype.clearCompounds=function(){var s={},E={};this.performDFSOnCompounds();for(var u=0;u<this.compoundOrder.length;u++)E[this.compoundOrder[u].id]=this.compoundOrder[u],s[this.compoundOrder[u].id]=[].concat(this.compoundOrder[u].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[u].getChild()),this.compoundOrder[u].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,E)},x.prototype.clearZeroDegreeMembers=function(){var s=this,E=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(u){var m=s.idToDummyNode[u];if(E[u]=s.tileNodes(s.memberGroups[u],m.paddingLeft+m.paddingRight),m.rect.width=E[u].width,m.rect.height=E[u].height,m.setCenter(E[u].centerX,E[u].centerY),m.labelMarginLeft=0,m.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var f=m.rect.width,w=m.rect.height;m.labelWidth&&(m.labelPosHorizontal=="left"?(m.rect.x-=m.labelWidth,m.setWidth(f+m.labelWidth),m.labelMarginLeft=m.labelWidth):m.labelPosHorizontal=="center"&&m.labelWidth>f?(m.rect.x-=(m.labelWidth-f)/2,m.setWidth(m.labelWidth),m.labelMarginLeft=(m.labelWidth-f)/2):m.labelPosHorizontal=="right"&&m.setWidth(f+m.labelWidth)),m.labelHeight&&(m.labelPosVertical=="top"?(m.rect.y-=m.labelHeight,m.setHeight(w+m.labelHeight),m.labelMarginTop=m.labelHeight):m.labelPosVertical=="center"&&m.labelHeight>w?(m.rect.y-=(m.labelHeight-w)/2,m.setHeight(m.labelHeight),m.labelMarginTop=(m.labelHeight-w)/2):m.labelPosVertical=="bottom"&&m.setHeight(w+m.labelHeight))}})},x.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var E=this.compoundOrder[s],u=E.id,m=E.paddingLeft,f=E.paddingTop,w=E.labelMarginLeft,C=E.labelMarginTop;this.adjustLocations(this.tiledMemberPack[u],E.rect.x,E.rect.y,m,f,w,C)}},x.prototype.repopulateZeroDegreeMembers=function(){var s=this,E=this.tiledZeroDegreePack;Object.keys(E).forEach(function(u){var m=s.idToDummyNode[u],f=m.paddingLeft,w=m.paddingTop,C=m.labelMarginLeft,G=m.labelMarginTop;s.adjustLocations(E[u],m.rect.x,m.rect.y,f,w,C,G)})},x.prototype.getToBeTiled=function(s){var E=s.id;if(this.toBeTiled[E]!=null)return this.toBeTiled[E];var u=s.getChild();if(u==null)return this.toBeTiled[E]=!1,!1;for(var m=u.getNodes(),f=0;f<m.length;f++){var w=m[f];if(this.getNodeDegree(w)>0)return this.toBeTiled[E]=!1,!1;if(w.getChild()!=null){if(!this.getToBeTiled(w))return this.toBeTiled[E]=!1,!1}else this.toBeTiled[w.id]=!1}return this.toBeTiled[E]=!0,!0},x.prototype.getNodeDegree=function(s){s.id;for(var E=s.getEdges(),u=0,m=0;m<E.length;m++){var f=E[m];f.getSource().id!==f.getTarget().id&&(u+=1)}return u},x.prototype.getNodeDegreeWithChildren=function(s){var E=this.getNodeDegree(s);if(s.getChild()==null)return E;for(var u=s.getChild().getNodes(),m=0;m<u.length;m++){var f=u[m];E+=this.getNodeDegreeWithChildren(f)}return E},x.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},x.prototype.fillCompexOrderByDFS=function(s){for(var E=0;E<s.length;E++){var u=s[E];u.getChild()!=null&&this.fillCompexOrderByDFS(u.getChild().getNodes()),this.getToBeTiled(u)&&this.compoundOrder.push(u)}},x.prototype.adjustLocations=function(s,E,u,m,f,w,C){u+=f+C;for(var G=E+=m+w,R=0;R<s.rows.length;R++){var M=s.rows[R];E=G;for(var X=0,S=0;S<M.length;S++){var B=M[S];B.rect.x=E,B.rect.y=u,E+=B.rect.width+s.horizontalPadding,B.rect.height>X&&(X=B.rect.height)}u+=X+s.verticalPadding}},x.prototype.tileCompoundMembers=function(s,E){var u=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(m){var f=E[m];if(u.tiledMemberPack[m]=u.tileNodes(s[m],f.paddingLeft+f.paddingRight),f.rect.width=u.tiledMemberPack[m].width,f.rect.height=u.tiledMemberPack[m].height,f.setCenter(u.tiledMemberPack[m].centerX,u.tiledMemberPack[m].centerY),f.labelMarginLeft=0,f.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var w=f.rect.width,C=f.rect.height;f.labelWidth&&(f.labelPosHorizontal=="left"?(f.rect.x-=f.labelWidth,f.setWidth(w+f.labelWidth),f.labelMarginLeft=f.labelWidth):f.labelPosHorizontal=="center"&&f.labelWidth>w?(f.rect.x-=(f.labelWidth-w)/2,f.setWidth(f.labelWidth),f.labelMarginLeft=(f.labelWidth-w)/2):f.labelPosHorizontal=="right"&&f.setWidth(w+f.labelWidth)),f.labelHeight&&(f.labelPosVertical=="top"?(f.rect.y-=f.labelHeight,f.setHeight(C+f.labelHeight),f.labelMarginTop=f.labelHeight):f.labelPosVertical=="center"&&f.labelHeight>C?(f.rect.y-=(f.labelHeight-C)/2,f.setHeight(f.labelHeight),f.labelMarginTop=(f.labelHeight-C)/2):f.labelPosVertical=="bottom"&&f.setHeight(C+f.labelHeight))}})},x.prototype.tileNodes=function(s,E){var u=this.tileNodesByFavoringDim(s,E,!0),m=this.tileNodesByFavoringDim(s,E,!1),f=this.getOrgRatio(u);return this.getOrgRatio(m)<f?m:u},x.prototype.getOrgRatio=function(s){var E=s.width/s.height;return E<1&&(E=1/E),E},x.prototype.calcIdealRowWidth=function(s,E){var u=o.TILING_PADDING_VERTICAL,m=o.TILING_PADDING_HORIZONTAL,f=s.length,w=0,C=0,G=0;s.forEach(function(D){w+=D.getWidth(),C+=D.getHeight(),D.getWidth()>G&&(G=D.getWidth())});var R,M=w/f,X=C/f,S=Math.pow(u-m,2)+4*(M+m)*(X+u)*f,B=(m-u+Math.sqrt(S))/(2*(M+m));E?(R=Math.ceil(B))==B&&R++:R=Math.floor(B);var W=R*(M+m)-m;return G>W&&(W=G),W+=2*m},x.prototype.tileNodesByFavoringDim=function(s,E,u){var m=o.TILING_PADDING_VERTICAL,f=o.TILING_PADDING_HORIZONTAL,w=o.TILING_COMPARE_BY,C={rows:[],rowWidth:[],rowHeight:[],width:0,height:E,verticalPadding:m,horizontalPadding:f,centerX:0,centerY:0};w&&(C.idealRowWidth=this.calcIdealRowWidth(s,u));var G=function(D){return D.rect.width*D.rect.height},R=function(D,I){return G(I)-G(D)};s.sort(function(D,I){var k=R;return C.idealRowWidth?(k=w)(D.id,I.id):k(D,I)});for(var M=0,X=0,S=0;S<s.length;S++)M+=(B=s[S]).getCenterX(),X+=B.getCenterY();for(C.centerX=M/s.length,C.centerY=X/s.length,S=0;S<s.length;S++){var B=s[S];if(C.rows.length==0)this.insertNodeToRow(C,B,0,E);else if(this.canAddHorizontal(C,B.rect.width,B.rect.height)){var W=C.rows.length-1;C.idealRowWidth||(W=this.getShortestRowIndex(C)),this.insertNodeToRow(C,B,W,E)}else this.insertNodeToRow(C,B,C.rows.length,E);this.shiftToLastRow(C)}return C},x.prototype.insertNodeToRow=function(s,E,u,m){var f=m;u==s.rows.length&&(s.rows.push([]),s.rowWidth.push(f),s.rowHeight.push(0));var w=s.rowWidth[u]+E.rect.width;s.rows[u].length>0&&(w+=s.horizontalPadding),s.rowWidth[u]=w,s.width<w&&(s.width=w);var C=E.rect.height;u>0&&(C+=s.verticalPadding);var G=0;C>s.rowHeight[u]&&(G=s.rowHeight[u],s.rowHeight[u]=C,G=s.rowHeight[u]-G),s.height+=G,s.rows[u].push(E)},x.prototype.getShortestRowIndex=function(s){for(var E=-1,u=Number.MAX_VALUE,m=0;m<s.rows.length;m++)s.rowWidth[m]<u&&(E=m,u=s.rowWidth[m]);return E},x.prototype.getLongestRowIndex=function(s){for(var E=-1,u=Number.MIN_VALUE,m=0;m<s.rows.length;m++)s.rowWidth[m]>u&&(E=m,u=s.rowWidth[m]);return E},x.prototype.canAddHorizontal=function(s,E,u){if(s.idealRowWidth){var m=s.rows.length-1;return s.rowWidth[m]+E+s.horizontalPadding<=s.idealRowWidth}var f=this.getShortestRowIndex(s);if(f<0)return!0;var w=s.rowWidth[f];if(w+s.horizontalPadding+E<=s.width)return!0;var C,G,R=0;return s.rowHeight[f]<u&&f>0&&(R=u+s.verticalPadding-s.rowHeight[f]),C=s.width-w>=E+s.horizontalPadding?(s.height+R)/(w+E+s.horizontalPadding):(s.height+R)/s.width,R=u+s.verticalPadding,(G=s.width<E?(s.height+R)/E:(s.height+R)/s.width)<1&&(G=1/G),C<1&&(C=1/C),C<G},x.prototype.shiftToLastRow=function(s){var E=this.getLongestRowIndex(s),u=s.rowWidth.length-1,m=s.rows[E],f=m[m.length-1],w=f.width+s.horizontalPadding;if(s.width-s.rowWidth[u]>w&&E!=u){m.splice(-1,1),s.rows[u].push(f),s.rowWidth[E]=s.rowWidth[E]-w,s.rowWidth[u]=s.rowWidth[u]+w,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var C=Number.MIN_VALUE,G=0;G<m.length;G++)m[G].height>C&&(C=m[G].height);E>0&&(C+=s.verticalPadding);var R=s.rowHeight[E]+s.rowHeight[u];s.rowHeight[E]=C,s.rowHeight[u]<f.height+s.verticalPadding&&(s.rowHeight[u]=f.height+s.verticalPadding);var M=s.rowHeight[E]+s.rowHeight[u];s.height+=M-R,this.shiftToLastRow(s)}},x.prototype.tilingPreLayout=function(){o.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},x.prototype.tilingPostLayout=function(){o.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},x.prototype.reduceTrees=function(){for(var s,E=[],u=!0;u;){var m=this.graphManager.getAllNodes(),f=[];u=!1;for(var w=0;w<m.length;w++)if((s=m[w]).getEdges().length==1&&!s.getEdges()[0].isInterGraph&&s.getChild()==null){if(o.PURE_INCREMENTAL){var C=s.getEdges()[0].getOtherEnd(s),G=new N(s.getCenterX()-C.getCenterX(),s.getCenterY()-C.getCenterY());f.push([s,s.getEdges()[0],s.getOwner(),G])}else f.push([s,s.getEdges()[0],s.getOwner()]);u=!0}if(u==1){for(var R=[],M=0;M<f.length;M++)f[M][0].getEdges().length==1&&(R.push(f[M]),f[M][0].getOwner().remove(f[M][0]));E.push(R),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=E},x.prototype.growTree=function(s){for(var E,u=s[s.length-1],m=0;m<u.length;m++)E=u[m],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},x.prototype.findPlaceforPrunedNode=function(s){var E,u,m=s[0];if(u=m==s[1].source?s[1].target:s[1].source,o.PURE_INCREMENTAL)m.setCenter(u.getCenterX()+s[3].getWidth(),u.getCenterY()+s[3].getHeight());else{var f=u.startX,w=u.finishX,C=u.startY,G=u.finishY,R=[0,0,0,0];if(C>0)for(var M=f;M<=w;M++)R[0]+=this.grid[M][C-1].length+this.grid[M][C].length-1;if(w<this.grid.length-1)for(M=C;M<=G;M++)R[1]+=this.grid[w+1][M].length+this.grid[w][M].length-1;if(G<this.grid[0].length-1)for(M=f;M<=w;M++)R[2]+=this.grid[M][G+1].length+this.grid[M][G].length-1;if(f>0)for(M=C;M<=G;M++)R[3]+=this.grid[f-1][M].length+this.grid[f][M].length-1;for(var X,S,B=P.MAX_VALUE,W=0;W<R.length;W++)R[W]<B?(B=R[W],X=1,S=W):R[W]==B&&X++;if(X==3&&B==0)R[0]==0&&R[1]==0&&R[2]==0?E=1:R[0]==0&&R[1]==0&&R[3]==0?E=0:R[0]==0&&R[2]==0&&R[3]==0?E=3:R[1]==0&&R[2]==0&&R[3]==0&&(E=2);else if(X==2&&B==0){var D=Math.floor(2*Math.random());E=R[0]==0&&R[1]==0?D==0?0:1:R[0]==0&&R[2]==0?D==0?0:2:R[0]==0&&R[3]==0?D==0?0:3:R[1]==0&&R[2]==0?D==0?1:2:R[1]==0&&R[3]==0?D==0?1:3:D==0?2:3}else E=X==4&&B==0?D=Math.floor(4*Math.random()):S;E==0?m.setCenter(u.getCenterX(),u.getCenterY()-u.getHeight()/2-n.DEFAULT_EDGE_LENGTH-m.getHeight()/2):E==1?m.setCenter(u.getCenterX()+u.getWidth()/2+n.DEFAULT_EDGE_LENGTH+m.getWidth()/2,u.getCenterY()):E==2?m.setCenter(u.getCenterX(),u.getCenterY()+u.getHeight()/2+n.DEFAULT_EDGE_LENGTH+m.getHeight()/2):m.setCenter(u.getCenterX()-u.getWidth()/2-n.DEFAULT_EDGE_LENGTH-m.getWidth()/2,u.getCenterY())}},i.exports=x},991:(i,a,t)=>{var r=t(551).FDLayoutNode,e=t(551).IMath;function l(d,o,c,n){r.call(this,d,o,c,n)}for(var h in l.prototype=Object.create(r.prototype),r)l[h]=r[h];l.prototype.calculateDisplacement=function(){var d=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=d.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=d.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=d.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=d.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>d.coolingFactor*d.maxNodeDisplacement&&(this.displacementX=d.coolingFactor*d.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>d.coolingFactor*d.maxNodeDisplacement&&(this.displacementY=d.coolingFactor*d.maxNodeDisplacement*e.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},l.prototype.propogateDisplacementToChildren=function(d,o){for(var c,n=this.getChild().getNodes(),y=0;y<n.length;y++)(c=n[y]).getChild()==null?(c.displacementX+=d,c.displacementY+=o):c.propogateDisplacementToChildren(d,o)},l.prototype.move=function(){var d=this.graphManager.getLayout();this.child!=null&&this.child.getNodes().length!=0||(this.moveBy(this.displacementX,this.displacementY),d.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},l.prototype.setPred1=function(d){this.pred1=d},l.prototype.getPred1=function(){return pred1},l.prototype.getPred2=function(){return pred2},l.prototype.setNext=function(d){this.next=d},l.prototype.getNext=function(){return next},l.prototype.setProcessed=function(d){this.processed=d},l.prototype.isProcessed=function(){return processed},i.exports=l},902:(i,a,t)=>{function r(c){if(Array.isArray(c)){for(var n=0,y=Array(c.length);n<c.length;n++)y[n]=c[n];return y}return Array.from(c)}var e=t(806),l=t(551).LinkedList,h=t(551).Matrix,d=t(551).SVD;function o(){}o.handleConstraints=function(c){var n={};n.fixedNodeConstraint=c.constraints.fixedNodeConstraint,n.alignmentConstraint=c.constraints.alignmentConstraint,n.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var y=new Map,v=new Map,p=[],N=[],_=c.getAllNodes(),P=0,U=0;U<_.length;U++){var F=_[U];F.getChild()==null&&(v.set(F.id,P++),p.push(F.getCenterX()),N.push(F.getCenterY()),y.set(F.id,F))}n.relativePlacementConstraint&&n.relativePlacementConstraint.forEach(function(O){O.gap||O.gap==0||(O.left?O.gap=e.DEFAULT_EDGE_LENGTH+y.get(O.left).getWidth()/2+y.get(O.right).getWidth()/2:O.gap=e.DEFAULT_EDGE_LENGTH+y.get(O.top).getHeight()/2+y.get(O.bottom).getHeight()/2)});var H=function(O){var Y=0,Z=0;return O.forEach(function(j){Y+=p[v.get(j)],Z+=N[v.get(j)]}),{x:Y/O.size,y:Z/O.size}},V=function(O,Y,Z,j,tt){var ut=new Map;O.forEach(function(q,K){ut.set(K,0)}),O.forEach(function(q,K){q.forEach(function(gt){ut.set(gt.id,ut.get(gt.id)+1)})});var rt=new Map,pt=new Map,At=new l;ut.forEach(function(q,K){q==0?(At.push(K),Z||(Y=="horizontal"?rt.set(K,v.has(K)?p[v.get(K)]:j.get(K)):rt.set(K,v.has(K)?N[v.get(K)]:j.get(K)))):rt.set(K,Number.NEGATIVE_INFINITY),Z&&pt.set(K,new Set([K]))}),Z&&tt.forEach(function(q){var K=[];if(q.forEach(function(nt){Z.has(nt)&&K.push(nt)}),K.length>0){var gt=0;K.forEach(function(nt){Y=="horizontal"?(rt.set(nt,v.has(nt)?p[v.get(nt)]:j.get(nt)),gt+=rt.get(nt)):(rt.set(nt,v.has(nt)?N[v.get(nt)]:j.get(nt)),gt+=rt.get(nt))}),gt/=K.length,q.forEach(function(nt){Z.has(nt)||rt.set(nt,gt)})}else{var Tt=0;q.forEach(function(nt){Tt+=Y=="horizontal"?v.has(nt)?p[v.get(nt)]:j.get(nt):v.has(nt)?N[v.get(nt)]:j.get(nt)}),Tt/=q.length,q.forEach(function(nt){rt.set(nt,Tt)})}});for(var Ut=function(){var q=At.shift();O.get(q).forEach(function(K){if(rt.get(K.id)<rt.get(q)+K.gap)if(Z&&Z.has(K.id)){var gt=void 0;if(gt=Y=="horizontal"?v.has(K.id)?p[v.get(K.id)]:j.get(K.id):v.has(K.id)?N[v.get(K.id)]:j.get(K.id),rt.set(K.id,gt),gt<rt.get(q)+K.gap){var Tt=rt.get(q)+K.gap-gt;pt.get(q).forEach(function(nt){rt.set(nt,rt.get(nt)-Tt)})}}else rt.set(K.id,rt.get(q)+K.gap);ut.set(K.id,ut.get(K.id)-1),ut.get(K.id)==0&&At.push(K.id),Z&&pt.set(K.id,function(nt,Ct){var St=new Set(nt),Dt=!0,Pt=!1,Rt=void 0;try{for(var _t,lt=Ct[Symbol.iterator]();!(Dt=(_t=lt.next()).done);Dt=!0){var Ft=_t.value;St.add(Ft)}}catch(Ht){Pt=!0,Rt=Ht}finally{try{!Dt&&lt.return&&lt.return()}finally{if(Pt)throw Rt}}return St}(pt.get(q),pt.get(K.id)))})};At.length!=0;)Ut();if(Z){var Ot=new Set;O.forEach(function(q,K){q.length==0&&Ot.add(K)});var wt=[];pt.forEach(function(q,K){if(Ot.has(K)){var gt=!1,Tt=!0,nt=!1,Ct=void 0;try{for(var St,Dt=q[Symbol.iterator]();!(Tt=(St=Dt.next()).done);Tt=!0){var Pt=St.value;Z.has(Pt)&&(gt=!0)}}catch(lt){nt=!0,Ct=lt}finally{try{!Tt&&Dt.return&&Dt.return()}finally{if(nt)throw Ct}}if(!gt){var Rt=!1,_t=void 0;wt.forEach(function(lt,Ft){lt.has([].concat(r(q))[0])&&(Rt=!0,_t=Ft)}),Rt?q.forEach(function(lt){wt[_t].add(lt)}):wt.push(new Set(q))}}}),wt.forEach(function(q,K){var gt=Number.POSITIVE_INFINITY,Tt=Number.POSITIVE_INFINITY,nt=Number.NEGATIVE_INFINITY,Ct=Number.NEGATIVE_INFINITY,St=!0,Dt=!1,Pt=void 0;try{for(var Rt,_t=q[Symbol.iterator]();!(St=(Rt=_t.next()).done);St=!0){var lt=Rt.value,Ft=void 0;Ft=Y=="horizontal"?v.has(lt)?p[v.get(lt)]:j.get(lt):v.has(lt)?N[v.get(lt)]:j.get(lt);var Ht=rt.get(lt);Ft<gt&&(gt=Ft),Ft>nt&&(nt=Ft),Ht<Tt&&(Tt=Ht),Ht>Ct&&(Ct=Ht)}}catch(jt){Dt=!0,Pt=jt}finally{try{!St&&_t.return&&_t.return()}finally{if(Dt)throw Pt}}var $t=(gt+nt)/2-(Tt+Ct)/2,te=!0,qt=!1,Zt=void 0;try{for(var Jt,Wt=q[Symbol.iterator]();!(te=(Jt=Wt.next()).done);te=!0){var re=Jt.value;rt.set(re,rt.get(re)+$t)}}catch(jt){qt=!0,Zt=jt}finally{try{!te&&Wt.return&&Wt.return()}finally{if(qt)throw Zt}}})}return rt},x=function(O){var Y=0,Z=0,j=0,tt=0;if(O.forEach(function(At){At.left?p[v.get(At.left)]-p[v.get(At.right)]>=0?Y++:Z++:N[v.get(At.top)]-N[v.get(At.bottom)]>=0?j++:tt++}),Y>Z&&j>tt)for(var ut=0;ut<v.size;ut++)p[ut]=-1*p[ut],N[ut]=-1*N[ut];else if(Y>Z)for(var rt=0;rt<v.size;rt++)p[rt]=-1*p[rt];else if(j>tt)for(var pt=0;pt<v.size;pt++)N[pt]=-1*N[pt]},ot=function(O){var Y=[],Z=new l,j=new Set,tt=0;return O.forEach(function(ut,rt){if(!j.has(rt)){Y[tt]=[];var pt=rt;for(Z.push(pt),j.add(pt),Y[tt].push(pt);Z.length!=0;)pt=Z.shift(),O.get(pt).forEach(function(At){j.has(At.id)||(Z.push(At.id),j.add(At.id),Y[tt].push(At.id))});tt++}}),Y},s=function(O){var Y=new Map;return O.forEach(function(Z,j){Y.set(j,[])}),O.forEach(function(Z,j){Z.forEach(function(tt){Y.get(j).push(tt),Y.get(tt.id).push({id:j,gap:tt.gap,direction:tt.direction})})}),Y},E=function(O){var Y=new Map;return O.forEach(function(Z,j){Y.set(j,[])}),O.forEach(function(Z,j){Z.forEach(function(tt){Y.get(tt.id).push({id:j,gap:tt.gap,direction:tt.direction})})}),Y},u=[],m=[],f=!1,w=!1,C=new Set,G=new Map,R=new Map,M=[];if(n.fixedNodeConstraint&&n.fixedNodeConstraint.forEach(function(O){C.add(O.nodeId)}),n.relativePlacementConstraint&&(n.relativePlacementConstraint.forEach(function(O){O.left?(G.has(O.left)?G.get(O.left).push({id:O.right,gap:O.gap,direction:"horizontal"}):G.set(O.left,[{id:O.right,gap:O.gap,direction:"horizontal"}]),G.has(O.right)||G.set(O.right,[])):(G.has(O.top)?G.get(O.top).push({id:O.bottom,gap:O.gap,direction:"vertical"}):G.set(O.top,[{id:O.bottom,gap:O.gap,direction:"vertical"}]),G.has(O.bottom)||G.set(O.bottom,[]))}),R=s(G),M=ot(R)),e.TRANSFORM_ON_CONSTRAINT_HANDLING){if(n.fixedNodeConstraint&&n.fixedNodeConstraint.length>1)n.fixedNodeConstraint.forEach(function(O,Y){u[Y]=[O.position.x,O.position.y],m[Y]=[p[v.get(O.nodeId)],N[v.get(O.nodeId)]]}),f=!0;else if(n.alignmentConstraint)(function(){var O=0;if(n.alignmentConstraint.vertical){for(var Y=n.alignmentConstraint.vertical,Z=function(pt){var At=new Set;Y[pt].forEach(function(wt){At.add(wt)});var Ut=new Set([].concat(r(At)).filter(function(wt){return C.has(wt)})),Ot=void 0;Ot=Ut.size>0?p[v.get(Ut.values().next().value)]:H(At).x,Y[pt].forEach(function(wt){u[O]=[Ot,N[v.get(wt)]],m[O]=[p[v.get(wt)],N[v.get(wt)]],O++})},j=0;j<Y.length;j++)Z(j);f=!0}if(n.alignmentConstraint.horizontal){for(var tt=n.alignmentConstraint.horizontal,ut=function(pt){var At=new Set;tt[pt].forEach(function(wt){At.add(wt)});var Ut=new Set([].concat(r(At)).filter(function(wt){return C.has(wt)})),Ot=void 0;Ot=Ut.size>0?p[v.get(Ut.values().next().value)]:H(At).y,tt[pt].forEach(function(wt){u[O]=[p[v.get(wt)],Ot],m[O]=[p[v.get(wt)],N[v.get(wt)]],O++})},rt=0;rt<tt.length;rt++)ut(rt);f=!0}n.relativePlacementConstraint&&(w=!0)})();else if(n.relativePlacementConstraint){for(var X=0,S=0,B=0;B<M.length;B++)M[B].length>X&&(X=M[B].length,S=B);if(X<R.size/2)x(n.relativePlacementConstraint),f=!1,w=!1;else{var W=new Map,D=new Map,I=[];M[S].forEach(function(O){G.get(O).forEach(function(Y){Y.direction=="horizontal"?(W.has(O)?W.get(O).push(Y):W.set(O,[Y]),W.has(Y.id)||W.set(Y.id,[]),I.push({left:O,right:Y.id})):(D.has(O)?D.get(O).push(Y):D.set(O,[Y]),D.has(Y.id)||D.set(Y.id,[]),I.push({top:O,bottom:Y.id}))})}),x(I),w=!1;var k=V(W,"horizontal"),z=V(D,"vertical");M[S].forEach(function(O,Y){m[Y]=[p[v.get(O)],N[v.get(O)]],u[Y]=[],k.has(O)?u[Y][0]=k.get(O):u[Y][0]=p[v.get(O)],z.has(O)?u[Y][1]=z.get(O):u[Y][1]=N[v.get(O)]}),f=!0}}if(f){for(var Q,J=h.transpose(u),at=h.transpose(m),mt=0;mt<J.length;mt++)J[mt]=h.multGamma(J[mt]),at[mt]=h.multGamma(at[mt]);var $=h.multMat(J,h.transpose(at)),Gt=d.svd($);Q=h.multMat(Gt.V,h.transpose(Gt.U));for(var ft=0;ft<v.size;ft++){var xt=[p[ft],N[ft]],Yt=[Q[0][0],Q[1][0]],Mt=[Q[0][1],Q[1][1]];p[ft]=h.dotProduct(xt,Yt),N[ft]=h.dotProduct(xt,Mt)}w&&x(n.relativePlacementConstraint)}}if(e.ENFORCE_CONSTRAINTS){if(n.fixedNodeConstraint&&n.fixedNodeConstraint.length>0){var kt={x:0,y:0};n.fixedNodeConstraint.forEach(function(O,Y){var Z,j,tt={x:p[v.get(O.nodeId)],y:N[v.get(O.nodeId)]},ut=O.position,rt=(j=tt,{x:(Z=ut).x-j.x,y:Z.y-j.y});kt.x+=rt.x,kt.y+=rt.y}),kt.x/=n.fixedNodeConstraint.length,kt.y/=n.fixedNodeConstraint.length,p.forEach(function(O,Y){p[Y]+=kt.x}),N.forEach(function(O,Y){N[Y]+=kt.y}),n.fixedNodeConstraint.forEach(function(O){p[v.get(O.nodeId)]=O.position.x,N[v.get(O.nodeId)]=O.position.y})}if(n.alignmentConstraint){if(n.alignmentConstraint.vertical)for(var ct=n.alignmentConstraint.vertical,ht=function(O){var Y=new Set;ct[O].forEach(function(tt){Y.add(tt)});var Z=new Set([].concat(r(Y)).filter(function(tt){return C.has(tt)})),j=void 0;j=Z.size>0?p[v.get(Z.values().next().value)]:H(Y).x,Y.forEach(function(tt){C.has(tt)||(p[v.get(tt)]=j)})},st=0;st<ct.length;st++)ht(st);if(n.alignmentConstraint.horizontal)for(var yt=n.alignmentConstraint.horizontal,vt=function(O){var Y=new Set;yt[O].forEach(function(tt){Y.add(tt)});var Z=new Set([].concat(r(Y)).filter(function(tt){return C.has(tt)})),j=void 0;j=Z.size>0?N[v.get(Z.values().next().value)]:H(Y).y,Y.forEach(function(tt){C.has(tt)||(N[v.get(tt)]=j)})},Et=0;Et<yt.length;Et++)vt(Et)}n.relativePlacementConstraint&&function(){var O=new Map,Y=new Map,Z=new Map,j=new Map,tt=new Map,ut=new Map,rt=new Set,pt=new Set;if(C.forEach(function(it){rt.add(it),pt.add(it)}),n.alignmentConstraint){if(n.alignmentConstraint.vertical)for(var At=n.alignmentConstraint.vertical,Ut=function(it){Z.set("dummy"+it,[]),At[it].forEach(function(It){O.set(It,"dummy"+it),Z.get("dummy"+it).push(It),C.has(It)&&rt.add("dummy"+it)}),tt.set("dummy"+it,p[v.get(At[it][0])])},Ot=0;Ot<At.length;Ot++)Ut(Ot);if(n.alignmentConstraint.horizontal)for(var wt=n.alignmentConstraint.horizontal,q=function(it){j.set("dummy"+it,[]),wt[it].forEach(function(It){Y.set(It,"dummy"+it),j.get("dummy"+it).push(It),C.has(It)&&pt.add("dummy"+it)}),ut.set("dummy"+it,N[v.get(wt[it][0])])},K=0;K<wt.length;K++)q(K)}var gt=new Map,Tt=new Map,nt=function(it){G.get(it).forEach(function(It){var Xt=void 0,Vt=void 0;It.direction=="horizontal"?(Xt=O.get(it)?O.get(it):it,Vt=O.get(It.id)?{id:O.get(It.id),gap:It.gap,direction:It.direction}:It,gt.has(Xt)?gt.get(Xt).push(Vt):gt.set(Xt,[Vt]),gt.has(Vt.id)||gt.set(Vt.id,[])):(Xt=Y.get(it)?Y.get(it):it,Vt=Y.get(It.id)?{id:Y.get(It.id),gap:It.gap,direction:It.direction}:It,Tt.has(Xt)?Tt.get(Xt).push(Vt):Tt.set(Xt,[Vt]),Tt.has(Vt.id)||Tt.set(Vt.id,[]))})},Ct=!0,St=!1,Dt=void 0;try{for(var Pt,Rt=G.keys()[Symbol.iterator]();!(Ct=(Pt=Rt.next()).done);Ct=!0)nt(Pt.value)}catch(it){St=!0,Dt=it}finally{try{!Ct&&Rt.return&&Rt.return()}finally{if(St)throw Dt}}var _t=s(gt),lt=s(Tt),Ft=ot(_t),Ht=ot(lt),$t=E(gt),te=E(Tt),qt=[],Zt=[];Ft.forEach(function(it,It){qt[It]=[],it.forEach(function(Xt){$t.get(Xt).length==0&&qt[It].push(Xt)})}),Ht.forEach(function(it,It){Zt[It]=[],it.forEach(function(Xt){te.get(Xt).length==0&&Zt[It].push(Xt)})});var Jt=V(gt,"horizontal",rt,tt,qt),Wt=V(Tt,"vertical",pt,ut,Zt),re=function(it){Z.get(it)?Z.get(it).forEach(function(It){p[v.get(It)]=Jt.get(it)}):p[v.get(it)]=Jt.get(it)},jt=!0,ge=!1,ue=void 0;try{for(var pe,ae=Jt.keys()[Symbol.iterator]();!(jt=(pe=ae.next()).done);jt=!0)re(pe.value)}catch(it){ge=!0,ue=it}finally{try{!jt&&ae.return&&ae.return()}finally{if(ge)throw ue}}var Ue=function(it){j.get(it)?j.get(it).forEach(function(It){N[v.get(It)]=Wt.get(it)}):N[v.get(it)]=Wt.get(it)},se=!0,fe=!1,me=void 0;try{for(var ye,he=Wt.keys()[Symbol.iterator]();!(se=(ye=he.next()).done);se=!0)Ue(ye.value)}catch(it){fe=!0,me=it}finally{try{!se&&he.return&&he.return()}finally{if(fe)throw me}}}()}for(var Nt=0;Nt<_.length;Nt++){var Lt=_[Nt];Lt.getChild()==null&&Lt.setCenter(p[v.get(Lt.id)],N[v.get(Lt.id)])}},i.exports=o},551:i=>{i.exports=L}},T={},g=function i(a){var t=T[a];if(t!==void 0)return t.exports;var r=T[a]={exports:{}};return b[a](r,r.exports,i),r.exports}(45),g;var b,T,g},Ne.exports=A(nr()));var A}const or=er(rr.exports=function(A){return L={658:g=>{g.exports=Object.assign!=null?Object.assign.bind(Object):function(i){for(var a=arguments.length,t=Array(a>1?a-1:0),r=1;r<a;r++)t[r-1]=arguments[r];return t.forEach(function(e){Object.keys(e).forEach(function(l){return i[l]=e[l]})}),i}},548:(g,i,a)=>{var t=function(l,h){if(Array.isArray(l))return l;if(Symbol.iterator in Object(l))return function(d,o){var c=[],n=!0,y=!1,v=void 0;try{for(var p,N=d[Symbol.iterator]();!(n=(p=N.next()).done)&&(c.push(p.value),!o||c.length!==o);n=!0);}catch(_){y=!0,v=_}finally{try{!n&&N.return&&N.return()}finally{if(y)throw v}}return c}(l,h);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=a(140).layoutBase.LinkedList,e={getTopMostNodes:function(l){for(var h={},d=0;d<l.length;d++)h[l[d].id()]=!0;var o=l.filter(function(c,n){typeof c=="number"&&(c=n);for(var y=c.parent()[0];y!=null;){if(h[y.id()])return!1;y=y.parent()[0]}return!0});return o},connectComponents:function(l,h,d,o){var c=new r,n=new Set,y=[],v=void 0,p=void 0,N=void 0,_=!1,P=1,U=[],F=[],H=function(){var V=l.collection();F.push(V);var x=d[0],ot=l.collection();ot.merge(x).merge(x.descendants().intersection(h)),y.push(x),ot.forEach(function(u){c.push(u),n.add(u),V.merge(u)});for(var s=function(){x=c.shift();var u=l.collection();x.neighborhood().nodes().forEach(function(w){h.intersection(x.edgesWith(w)).length>0&&u.merge(w)});for(var m=0;m<u.length;m++){var f=u[m];(v=d.intersection(f.union(f.ancestors())))==null||n.has(v[0])||v.union(v.descendants()).forEach(function(w){c.push(w),n.add(w),V.merge(w),d.has(w)&&y.push(w)})}};c.length!=0;)s();if(V.forEach(function(u){h.intersection(u.connectedEdges()).forEach(function(m){V.has(m.source())&&V.has(m.target())&&V.merge(m)})}),y.length==d.length&&(_=!0),!_||_&&P>1){p=y[0],N=p.connectedEdges().length,y.forEach(function(u){u.connectedEdges().length<N&&(N=u.connectedEdges().length,p=u)}),U.push(p.id());var E=l.collection();E.merge(y[0]),y.forEach(function(u){E.merge(u)}),y=[],d=d.difference(E),P++}};do H();while(!_);return o&&U.length>0&&o.set("dummy"+(o.size+1),U),F},relocateComponent:function(l,h,d){if(!d.fixedNodeConstraint){var o=Number.POSITIVE_INFINITY,c=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY,y=Number.NEGATIVE_INFINITY;if(d.quality=="draft"){var v=!0,p=!1,N=void 0;try{for(var _,P=h.nodeIndexes[Symbol.iterator]();!(v=(_=P.next()).done);v=!0){var U=_.value,F=t(U,2),H=F[0],V=F[1],x=d.cy.getElementById(H);if(x){var ot=x.boundingBox(),s=h.xCoords[V]-ot.w/2,E=h.xCoords[V]+ot.w/2,u=h.yCoords[V]-ot.h/2,m=h.yCoords[V]+ot.h/2;s<o&&(o=s),E>c&&(c=E),u<n&&(n=u),m>y&&(y=m)}}}catch(R){p=!0,N=R}finally{try{!v&&P.return&&P.return()}finally{if(p)throw N}}var f=l.x-(c+o)/2,w=l.y-(y+n)/2;h.xCoords=h.xCoords.map(function(R){return R+f}),h.yCoords=h.yCoords.map(function(R){return R+w})}else{Object.keys(h).forEach(function(R){var M=h[R],X=M.getRect().x,S=M.getRect().x+M.getRect().width,B=M.getRect().y,W=M.getRect().y+M.getRect().height;X<o&&(o=X),S>c&&(c=S),B<n&&(n=B),W>y&&(y=W)});var C=l.x-(c+o)/2,G=l.y-(y+n)/2;Object.keys(h).forEach(function(R){var M=h[R];M.setCenter(M.getCenterX()+C,M.getCenterY()+G)})}}},calcBoundingBox:function(l,h,d,o){for(var c=Number.MAX_SAFE_INTEGER,n=Number.MIN_SAFE_INTEGER,y=Number.MAX_SAFE_INTEGER,v=Number.MIN_SAFE_INTEGER,p=void 0,N=void 0,_=void 0,P=void 0,U=l.descendants().not(":parent"),F=U.length,H=0;H<F;H++){var V=U[H];c>(p=h[o.get(V.id())]-V.width()/2)&&(c=p),n<(N=h[o.get(V.id())]+V.width()/2)&&(n=N),y>(_=d[o.get(V.id())]-V.height()/2)&&(y=_),v<(P=d[o.get(V.id())]+V.height()/2)&&(v=P)}var x={};return x.topLeftX=c,x.topLeftY=y,x.width=n-c,x.height=v-y,x},calcParentsWithoutChildren:function(l,h){var d=l.collection();return h.nodes(":parent").forEach(function(o){var c=!1;o.children().forEach(function(n){n.css("display")!="none"&&(c=!0)}),c||d.merge(o)}),d}};g.exports=e},816:(g,i,a)=>{var t=a(548),r=a(140).CoSELayout,e=a(140).CoSENode,l=a(140).layoutBase.PointD,h=a(140).layoutBase.DimensionD,d=a(140).layoutBase.LayoutConstants,o=a(140).layoutBase.FDLayoutConstants,c=a(140).CoSEConstants;g.exports={coseLayout:function(n,y){var v=n.cy,p=n.eles,N=p.nodes(),_=p.edges(),P=void 0,U=void 0,F=void 0,H={};n.randomize&&(P=y.nodeIndexes,U=y.xCoords,F=y.yCoords);var V=function(u){return typeof u=="function"},x=function(u,m){return V(u)?u(m):u},ot=t.calcParentsWithoutChildren(v,p);n.nestingFactor!=null&&(c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=n.nestingFactor),n.gravity!=null&&(c.DEFAULT_GRAVITY_STRENGTH=o.DEFAULT_GRAVITY_STRENGTH=n.gravity),n.numIter!=null&&(c.MAX_ITERATIONS=o.MAX_ITERATIONS=n.numIter),n.gravityRange!=null&&(c.DEFAULT_GRAVITY_RANGE_FACTOR=o.DEFAULT_GRAVITY_RANGE_FACTOR=n.gravityRange),n.gravityCompound!=null&&(c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH=n.gravityCompound),n.gravityRangeCompound!=null&&(c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=n.gravityRangeCompound),n.initialEnergyOnIncremental!=null&&(c.DEFAULT_COOLING_FACTOR_INCREMENTAL=o.DEFAULT_COOLING_FACTOR_INCREMENTAL=n.initialEnergyOnIncremental),n.tilingCompareBy!=null&&(c.TILING_COMPARE_BY=n.tilingCompareBy),n.quality=="proof"?d.QUALITY=2:d.QUALITY=0,c.NODE_DIMENSIONS_INCLUDE_LABELS=o.NODE_DIMENSIONS_INCLUDE_LABELS=d.NODE_DIMENSIONS_INCLUDE_LABELS=n.nodeDimensionsIncludeLabels,c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=d.DEFAULT_INCREMENTAL=!n.randomize,c.ANIMATE=o.ANIMATE=d.ANIMATE=n.animate,c.TILE=n.tile,c.TILING_PADDING_VERTICAL=typeof n.tilingPaddingVertical=="function"?n.tilingPaddingVertical.call():n.tilingPaddingVertical,c.TILING_PADDING_HORIZONTAL=typeof n.tilingPaddingHorizontal=="function"?n.tilingPaddingHorizontal.call():n.tilingPaddingHorizontal,c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=d.DEFAULT_INCREMENTAL=!0,c.PURE_INCREMENTAL=!n.randomize,d.DEFAULT_UNIFORM_LEAF_NODE_SIZES=n.uniformNodeDimensions,n.step=="transformed"&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,c.ENFORCE_CONSTRAINTS=!1,c.APPLY_LAYOUT=!1),n.step=="enforced"&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!0,c.APPLY_LAYOUT=!1),n.step=="cose"&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!1,c.APPLY_LAYOUT=!0),n.step=="all"&&(n.randomize?c.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!0,c.APPLY_LAYOUT=!0),n.fixedNodeConstraint||n.alignmentConstraint||n.relativePlacementConstraint?c.TREE_REDUCTION_ON_INCREMENTAL=!1:c.TREE_REDUCTION_ON_INCREMENTAL=!0;var s=new r,E=s.newGraphManager();return function u(m,f,w,C){for(var G=f.length,R=0;R<G;R++){var M=f[R],X=null;M.intersection(ot).length==0&&(X=M.children());var S=void 0,B=M.layoutDimensions({nodeDimensionsIncludeLabels:C.nodeDimensionsIncludeLabels});if(M.outerWidth()!=null&&M.outerHeight()!=null)if(C.randomize)if(M.isParent()){var W=t.calcBoundingBox(M,U,F,P);S=M.intersection(ot).length==0?m.add(new e(w.graphManager,new l(W.topLeftX,W.topLeftY),new h(W.width,W.height))):m.add(new e(w.graphManager,new l(W.topLeftX,W.topLeftY),new h(parseFloat(B.w),parseFloat(B.h))))}else S=m.add(new e(w.graphManager,new l(U[P.get(M.id())]-B.w/2,F[P.get(M.id())]-B.h/2),new h(parseFloat(B.w),parseFloat(B.h))));else S=m.add(new e(w.graphManager,new l(M.position("x")-B.w/2,M.position("y")-B.h/2),new h(parseFloat(B.w),parseFloat(B.h))));else S=m.add(new e(this.graphManager));S.id=M.data("id"),S.nodeRepulsion=x(C.nodeRepulsion,M),S.paddingLeft=parseInt(M.css("padding")),S.paddingTop=parseInt(M.css("padding")),S.paddingRight=parseInt(M.css("padding")),S.paddingBottom=parseInt(M.css("padding")),C.nodeDimensionsIncludeLabels&&(S.labelWidth=M.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,S.labelHeight=M.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,S.labelPosVertical=M.css("text-valign"),S.labelPosHorizontal=M.css("text-halign")),H[M.data("id")]=S,isNaN(S.rect.x)&&(S.rect.x=0),isNaN(S.rect.y)&&(S.rect.y=0),X!=null&&X.length>0&&u(w.getGraphManager().add(w.newGraph(),S),X,w,C)}}(E.addRoot(),t.getTopMostNodes(N),s,n),function(u,m,f){for(var w=0,C=0,G=0;G<f.length;G++){var R=f[G],M=H[R.data("source")],X=H[R.data("target")];if(M&&X&&M!==X&&M.getEdgesBetween(X).length==0){var S=m.add(u.newEdge(),M,X);S.id=R.id(),S.idealLength=x(n.idealEdgeLength,R),S.edgeElasticity=x(n.edgeElasticity,R),w+=S.idealLength,C++}}n.idealEdgeLength!=null&&(C>0?c.DEFAULT_EDGE_LENGTH=o.DEFAULT_EDGE_LENGTH=w/C:V(n.idealEdgeLength)?c.DEFAULT_EDGE_LENGTH=o.DEFAULT_EDGE_LENGTH=50:c.DEFAULT_EDGE_LENGTH=o.DEFAULT_EDGE_LENGTH=n.idealEdgeLength,c.MIN_REPULSION_DIST=o.MIN_REPULSION_DIST=o.DEFAULT_EDGE_LENGTH/10,c.DEFAULT_RADIAL_SEPARATION=o.DEFAULT_EDGE_LENGTH)}(s,E,_),function(u,m){m.fixedNodeConstraint&&(u.constraints.fixedNodeConstraint=m.fixedNodeConstraint),m.alignmentConstraint&&(u.constraints.alignmentConstraint=m.alignmentConstraint),m.relativePlacementConstraint&&(u.constraints.relativePlacementConstraint=m.relativePlacementConstraint)}(s,n),s.runLayout(),H}}},212:(g,i,a)=>{var t=function(){function c(n,y){for(var v=0;v<y.length;v++){var p=y[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(n,p.key,p)}}return function(n,y,v){return y&&c(n.prototype,y),v&&c(n,v),n}}(),r=a(658),e=a(548),l=a(657).spectralLayout,h=a(816).coseLayout,d=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(c){return 4500},idealEdgeLength:function(c){return 50},edgeElasticity:function(c){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),o=function(){function c(n){(function(y,v){if(!(y instanceof v))throw new TypeError("Cannot call a class as a function")})(this,c),this.options=r({},d,n)}return t(c,[{key:"run",value:function(){var n=this.options,y=n.cy,v=n.eles,p=[],N=[],_=void 0,P=[];!n.fixedNodeConstraint||Array.isArray(n.fixedNodeConstraint)&&n.fixedNodeConstraint.length!=0||(n.fixedNodeConstraint=void 0),n.alignmentConstraint&&(!n.alignmentConstraint.vertical||Array.isArray(n.alignmentConstraint.vertical)&&n.alignmentConstraint.vertical.length!=0||(n.alignmentConstraint.vertical=void 0),!n.alignmentConstraint.horizontal||Array.isArray(n.alignmentConstraint.horizontal)&&n.alignmentConstraint.horizontal.length!=0||(n.alignmentConstraint.horizontal=void 0)),!n.relativePlacementConstraint||Array.isArray(n.relativePlacementConstraint)&&n.relativePlacementConstraint.length!=0||(n.relativePlacementConstraint=void 0),(n.fixedNodeConstraint||n.alignmentConstraint||n.relativePlacementConstraint)&&(n.tile=!1,n.packComponents=!1);var U=void 0,F=!1;if(y.layoutUtilities&&n.packComponents&&((U=y.layoutUtilities("get"))||(U=y.layoutUtilities()),F=!0),v.nodes().length>0)if(F){var H=e.getTopMostNodes(n.eles.nodes());if((_=e.connectComponents(y,n.eles,H)).forEach(function(D){var I=D.boundingBox();P.push({x:I.x1+I.w/2,y:I.y1+I.h/2})}),n.randomize&&_.forEach(function(D){n.eles=D,p.push(l(n))}),n.quality=="default"||n.quality=="proof"){var V=y.collection();if(n.tile){var x=new Map,ot=0,s={nodeIndexes:x,xCoords:[],yCoords:[]},E=[];if(_.forEach(function(D,I){D.edges().length==0&&(D.nodes().forEach(function(k,z){V.merge(D.nodes()[z]),k.isParent()||(s.nodeIndexes.set(D.nodes()[z].id(),ot++),s.xCoords.push(D.nodes()[0].position().x),s.yCoords.push(D.nodes()[0].position().y))}),E.push(I))}),V.length>1){var u=V.boundingBox();P.push({x:u.x1+u.w/2,y:u.y1+u.h/2}),_.push(V),p.push(s);for(var m=E.length-1;m>=0;m--)_.splice(E[m],1),p.splice(E[m],1),P.splice(E[m],1)}}_.forEach(function(D,I){n.eles=D,N.push(h(n,p[I])),e.relocateComponent(P[I],N[I],n)})}else _.forEach(function(D,I){e.relocateComponent(P[I],p[I],n)});var f=new Set;if(_.length>1){var w=[],C=v.filter(function(D){return D.css("display")=="none"});_.forEach(function(D,I){var k=void 0;if(n.quality=="draft"&&(k=p[I].nodeIndexes),D.nodes().not(C).length>0){var z={edges:[],nodes:[]},Q=void 0;D.nodes().not(C).forEach(function(J){if(n.quality=="draft")if(J.isParent()){var at=e.calcBoundingBox(J,p[I].xCoords,p[I].yCoords,k);z.nodes.push({x:at.topLeftX,y:at.topLeftY,width:at.width,height:at.height})}else Q=k.get(J.id()),z.nodes.push({x:p[I].xCoords[Q]-J.boundingbox().w/2,y:p[I].yCoords[Q]-J.boundingbox().h/2,width:J.boundingbox().w,height:J.boundingbox().h});else N[I][J.id()]&&z.nodes.push({x:N[I][J.id()].getLeft(),y:N[I][J.id()].getTop(),width:N[I][J.id()].getWidth(),height:N[I][J.id()].getHeight()})}),D.edges().forEach(function(J){var at=J.source(),mt=J.target();if(at.css("display")!="none"&&mt.css("display")!="none")if(n.quality=="draft"){var $=k.get(at.id()),Gt=k.get(mt.id()),ft=[],xt=[];if(at.isParent()){var Yt=e.calcBoundingBox(at,p[I].xCoords,p[I].yCoords,k);ft.push(Yt.topLeftX+Yt.width/2),ft.push(Yt.topLeftY+Yt.height/2)}else ft.push(p[I].xCoords[$]),ft.push(p[I].yCoords[$]);if(mt.isParent()){var Mt=e.calcBoundingBox(mt,p[I].xCoords,p[I].yCoords,k);xt.push(Mt.topLeftX+Mt.width/2),xt.push(Mt.topLeftY+Mt.height/2)}else xt.push(p[I].xCoords[Gt]),xt.push(p[I].yCoords[Gt]);z.edges.push({startX:ft[0],startY:ft[1],endX:xt[0],endY:xt[1]})}else N[I][at.id()]&&N[I][mt.id()]&&z.edges.push({startX:N[I][at.id()].getCenterX(),startY:N[I][at.id()].getCenterY(),endX:N[I][mt.id()].getCenterX(),endY:N[I][mt.id()].getCenterY()})}),z.nodes.length>0&&(w.push(z),f.add(I))}});var G=U.packComponents(w,n.randomize).shifts;if(n.quality=="draft")p.forEach(function(D,I){var k=D.xCoords.map(function(Q){return Q+G[I].dx}),z=D.yCoords.map(function(Q){return Q+G[I].dy});D.xCoords=k,D.yCoords=z});else{var R=0;f.forEach(function(D){Object.keys(N[D]).forEach(function(I){var k=N[D][I];k.setCenter(k.getCenterX()+G[R].dx,k.getCenterY()+G[R].dy)}),R++})}}}else{var M=n.eles.boundingBox();if(P.push({x:M.x1+M.w/2,y:M.y1+M.h/2}),n.randomize){var X=l(n);p.push(X)}n.quality=="default"||n.quality=="proof"?(N.push(h(n,p[0])),e.relocateComponent(P[0],N[0],n)):e.relocateComponent(P[0],p[0],n)}var S=function(D,I){if(n.quality=="default"||n.quality=="proof"){typeof D=="number"&&(D=I);var k=void 0,z=void 0,Q=D.data("id");return N.forEach(function(at){Q in at&&(k={x:at[Q].getRect().getCenterX(),y:at[Q].getRect().getCenterY()},z=at[Q])}),n.nodeDimensionsIncludeLabels&&(z.labelWidth&&(z.labelPosHorizontal=="left"?k.x+=z.labelWidth/2:z.labelPosHorizontal=="right"&&(k.x-=z.labelWidth/2)),z.labelHeight&&(z.labelPosVertical=="top"?k.y+=z.labelHeight/2:z.labelPosVertical=="bottom"&&(k.y-=z.labelHeight/2))),k==null&&(k={x:D.position("x"),y:D.position("y")}),{x:k.x,y:k.y}}var J=void 0;return p.forEach(function(at){var mt=at.nodeIndexes.get(D.id());mt!=null&&(J={x:at.xCoords[mt],y:at.yCoords[mt]})}),J==null&&(J={x:D.position("x"),y:D.position("y")}),{x:J.x,y:J.y}};if(n.quality=="default"||n.quality=="proof"||n.randomize){var B=e.calcParentsWithoutChildren(y,v),W=v.filter(function(D){return D.css("display")=="none"});n.eles=v.not(W),v.nodes().not(":parent").not(W).layoutPositions(this,n,S),B.length>0&&B.forEach(function(D){D.position(S(D))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),c}();g.exports=o},657:(g,i,a)=>{var t=a(548),r=a(140).layoutBase.Matrix,e=a(140).layoutBase.SVD;g.exports={spectralLayout:function(l){var h=l.cy,d=l.eles,o=d.nodes(),c=d.nodes(":parent"),n=new Map,y=new Map,v=new Map,p=[],N=[],_=[],P=[],U=[],F=[],H=[],V=[],x=void 0,ot=1e8,s=1e-9,E=l.piTol,u=l.samplingType,m=l.nodeSeparation,f=void 0,w=function(ct,ht,st){for(var yt=[],vt=0,Et=0,Nt=0,Lt=void 0,O=[],Y=0,Z=1,j=0;j<x;j++)O[j]=ot;for(yt[Et]=ct,O[ct]=0;Et>=vt;){Nt=yt[vt++];for(var tt=p[Nt],ut=0;ut<tt.length;ut++)O[Lt=y.get(tt[ut])]==ot&&(O[Lt]=O[Nt]+1,yt[++Et]=Lt);F[Nt][ht]=O[Nt]*m}if(st){for(var rt=0;rt<x;rt++)F[rt][ht]<U[rt]&&(U[rt]=F[rt][ht]);for(var pt=0;pt<x;pt++)U[pt]>Y&&(Y=U[pt],Z=pt)}return Z};t.connectComponents(h,d,t.getTopMostNodes(o),n),c.forEach(function(ct){t.connectComponents(h,d,t.getTopMostNodes(ct.descendants().intersection(d)),n)});for(var C=0,G=0;G<o.length;G++)o[G].isParent()||y.set(o[G].id(),C++);var R=!0,M=!1,X=void 0;try{for(var S,B=n.keys()[Symbol.iterator]();!(R=(S=B.next()).done);R=!0){var W=S.value;y.set(W,C++)}}catch(ct){M=!0,X=ct}finally{try{!R&&B.return&&B.return()}finally{if(M)throw X}}for(var D=0;D<y.size;D++)p[D]=[];c.forEach(function(ct){for(var ht=ct.children().intersection(d);ht.nodes(":childless").length==0;)ht=ht.nodes()[0].children().intersection(d);var st=0,yt=ht.nodes(":childless")[0].connectedEdges().length;ht.nodes(":childless").forEach(function(vt,Et){vt.connectedEdges().length<yt&&(yt=vt.connectedEdges().length,st=Et)}),v.set(ct.id(),ht.nodes(":childless")[st].id())}),o.forEach(function(ct){var ht=void 0;ht=ct.isParent()?y.get(v.get(ct.id())):y.get(ct.id()),ct.neighborhood().nodes().forEach(function(st){d.intersection(ct.edgesWith(st)).length>0&&(st.isParent()?p[ht].push(v.get(st.id())):p[ht].push(st.id()))})});var I=function(ct){var ht=y.get(ct),st=void 0;n.get(ct).forEach(function(yt){st=h.getElementById(yt).isParent()?v.get(yt):yt,p[ht].push(st),p[y.get(st)].push(ct)})},k=!0,z=!1,Q=void 0;try{for(var J,at=n.keys()[Symbol.iterator]();!(k=(J=at.next()).done);k=!0)I(J.value)}catch(ct){z=!0,Q=ct}finally{try{!k&&at.return&&at.return()}finally{if(z)throw Q}}var mt=void 0;if((x=y.size)>2){f=x<l.sampleSize?x:l.sampleSize;for(var $=0;$<x;$++)F[$]=[];for(var Gt=0;Gt<f;Gt++)V[Gt]=[];return l.quality=="draft"||l.step=="all"?(function(ct){var ht=void 0;if(ct){ht=Math.floor(Math.random()*x);for(var st=0;st<x;st++)U[st]=ot;for(var yt=0;yt<f;yt++)P[yt]=ht,ht=w(ht,yt,ct)}else{(function(){for(var Z=0,j=0,tt=!1;j<f;){Z=Math.floor(Math.random()*x),tt=!1;for(var ut=0;ut<j;ut++)if(P[ut]==Z){tt=!0;break}tt||(P[j]=Z,j++)}})();for(var vt=0;vt<f;vt++)w(P[vt],vt,ct)}for(var Et=0;Et<x;Et++)for(var Nt=0;Nt<f;Nt++)F[Et][Nt]*=F[Et][Nt];for(var Lt=0;Lt<f;Lt++)H[Lt]=[];for(var O=0;O<f;O++)for(var Y=0;Y<f;Y++)H[O][Y]=F[P[Y]][O]}(u),function(){for(var ct=e.svd(H),ht=ct.S,st=ct.U,yt=ct.V,vt=ht[0]*ht[0]*ht[0],Et=[],Nt=0;Nt<f;Nt++){Et[Nt]=[];for(var Lt=0;Lt<f;Lt++)Et[Nt][Lt]=0,Nt==Lt&&(Et[Nt][Lt]=ht[Nt]/(ht[Nt]*ht[Nt]+vt/(ht[Nt]*ht[Nt])))}V=r.multMat(r.multMat(yt,Et),r.transpose(st))}(),function(){for(var ct=void 0,ht=void 0,st=[],yt=[],vt=[],Et=[],Nt=0;Nt<x;Nt++)st[Nt]=Math.random(),yt[Nt]=Math.random();st=r.normalize(st),yt=r.normalize(yt);for(var Lt=s,O=s,Y=void 0;;){for(var Z=0;Z<x;Z++)vt[Z]=st[Z];if(st=r.multGamma(r.multL(r.multGamma(vt),F,V)),ct=r.dotProduct(vt,st),st=r.normalize(st),Lt=r.dotProduct(vt,st),(Y=Math.abs(Lt/O))<=1+E&&Y>=1)break;O=Lt}for(var j=0;j<x;j++)vt[j]=st[j];for(O=s;;){for(var tt=0;tt<x;tt++)Et[tt]=yt[tt];if(Et=r.minusOp(Et,r.multCons(vt,r.dotProduct(vt,Et))),yt=r.multGamma(r.multL(r.multGamma(Et),F,V)),ht=r.dotProduct(Et,yt),yt=r.normalize(yt),Lt=r.dotProduct(Et,yt),(Y=Math.abs(Lt/O))<=1+E&&Y>=1)break;O=Lt}for(var ut=0;ut<x;ut++)Et[ut]=yt[ut];N=r.multCons(vt,Math.sqrt(Math.abs(ct))),_=r.multCons(Et,Math.sqrt(Math.abs(ht)))}(),mt={nodeIndexes:y,xCoords:N,yCoords:_}):(y.forEach(function(ct,ht){N.push(h.getElementById(ht).position("x")),_.push(h.getElementById(ht).position("y"))}),mt={nodeIndexes:y,xCoords:N,yCoords:_}),mt}var ft=y.keys(),xt=h.getElementById(ft.next().value),Yt=xt.position(),Mt=xt.outerWidth();if(N.push(Yt.x),_.push(Yt.y),x==2){var kt=h.getElementById(ft.next().value).outerWidth();N.push(Yt.x+Mt/2+kt/2+l.idealEdgeLength),_.push(Yt.y)}return mt={nodeIndexes:y,xCoords:N,yCoords:_}}}},579:(g,i,a)=>{var t=a(212),r=function(e){e&&e("layout","fcose",t)};typeof cytoscape<"u"&&r(cytoscape),g.exports=r},140:g=>{g.exports=A}},b={},T=function g(i){var a=b[i];if(a!==void 0)return a.exports;var t=b[i]={exports:{}};return L[i](t,t.exports,g),t.exports}(579),T;var L,b,T}(ir()));var Ae={L:"left",R:"right",T:"top",B:"bottom"},we={L:et(A=>`${A},${A/2} 0,${A} 0,0`,"L"),R:et(A=>`0,${A/2} ${A},0 ${A},${A}`,"R"),T:et(A=>`0,0 ${A},0 ${A/2},${A}`,"T"),B:et(A=>`${A/2},0 ${A},${A} 0,${A}`,"B")},ne={L:et((A,L)=>A-L+2,"L"),R:et((A,L)=>A-2,"R"),T:et((A,L)=>A-L+2,"T"),B:et((A,L)=>A-2,"B")},ar=et(function(A){return zt(A)?A==="L"?"R":"L":A==="T"?"B":"T"},"getOppositeArchitectureDirection"),Le=et(function(A){return A==="L"||A==="R"||A==="T"||A==="B"},"isArchitectureDirection"),zt=et(function(A){return A==="L"||A==="R"},"isArchitectureDirectionX"),Bt=et(function(A){return A==="T"||A==="B"},"isArchitectureDirectionY"),Me=et(function(A,L){const b=zt(A)&&Bt(L),T=Bt(A)&&zt(L);return b||T},"isArchitectureDirectionXY"),sr=et(function(A){const L=A[0],b=A[1],T=zt(L)&&Bt(b),g=Bt(L)&&zt(b);return T||g},"isArchitecturePairXY"),hr=et(function(A){return A!=="LL"&&A!=="RR"&&A!=="TT"&&A!=="BB"},"isValidArchitectureDirectionPair"),de=et(function(A,L){const b=`${A}${L}`;return hr(b)?b:void 0},"getArchitectureDirectionPair"),lr=et(function([A,L],b){const T=b[0],g=b[1];return zt(T)?Bt(g)?[A+(T==="L"?-1:1),L+(g==="T"?1:-1)]:[A+(T==="L"?-1:1),L]:zt(g)?[A+(g==="L"?1:-1),L+(T==="T"?1:-1)]:[A,L+(T==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),dr=et(function(A){return A==="LT"||A==="TL"?[1,1]:A==="BL"||A==="LB"?[1,-1]:A==="BR"||A==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),cr=et(function(A){return A.type==="service"},"isArchitectureService"),gr=et(function(A){return A.type==="junction"},"isArchitectureJunction"),Ce=et(A=>A.data(),"edgeData"),Qt=et(A=>A.data(),"nodeData"),Oe=qe.architecture,dt=new Qe(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:Oe,dataStructures:void 0,elements:{}})),ur=et(()=>{dt.reset(),je()},"clear"),pr=et(function({id:A,icon:L,in:b,title:T,iconText:g}){if(dt.records.registeredIds[A]!==void 0)throw new Error(`The service id [${A}] is already in use by another ${dt.records.registeredIds[A]}`);if(b!==void 0){if(A===b)throw new Error(`The service [${A}] cannot be placed within itself`);if(dt.records.registeredIds[b]===void 0)throw new Error(`The service [${A}]'s parent does not exist. Please make sure the parent is created before this service`);if(dt.records.registeredIds[b]==="node")throw new Error(`The service [${A}]'s parent is not a group`)}dt.records.registeredIds[A]="node",dt.records.nodes[A]={id:A,type:"service",icon:L,iconText:g,title:T,edges:[],in:b}},"addService"),fr=et(()=>Object.values(dt.records.nodes).filter(cr),"getServices"),mr=et(function({id:A,in:L}){dt.records.registeredIds[A]="node",dt.records.nodes[A]={id:A,type:"junction",edges:[],in:L}},"addJunction"),yr=et(()=>Object.values(dt.records.nodes).filter(gr),"getJunctions"),vr=et(()=>Object.values(dt.records.nodes),"getNodes"),Er=et(A=>dt.records.nodes[A],"getNode"),Nr=et(function({id:A,icon:L,in:b,title:T}){if(dt.records.registeredIds[A]!==void 0)throw new Error(`The group id [${A}] is already in use by another ${dt.records.registeredIds[A]}`);if(b!==void 0){if(A===b)throw new Error(`The group [${A}] cannot be placed within itself`);if(dt.records.registeredIds[b]===void 0)throw new Error(`The group [${A}]'s parent does not exist. Please make sure the parent is created before this group`);if(dt.records.registeredIds[b]==="node")throw new Error(`The group [${A}]'s parent is not a group`)}dt.records.registeredIds[A]="group",dt.records.groups[A]={id:A,icon:L,title:T,in:b}},"addGroup"),Tr=et(()=>Object.values(dt.records.groups),"getGroups"),Ar=et(function({lhsId:A,rhsId:L,lhsDir:b,rhsDir:T,lhsInto:g,rhsInto:i,lhsGroup:a,rhsGroup:t,title:r}){if(!Le(b))throw new Error(`Invalid direction given for left hand side of edge ${A}--${L}. Expected (L,R,T,B) got ${b}`);if(!Le(T))throw new Error(`Invalid direction given for right hand side of edge ${A}--${L}. Expected (L,R,T,B) got ${T}`);if(dt.records.nodes[A]===void 0&&dt.records.groups[A]===void 0)throw new Error(`The left-hand id [${A}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(dt.records.nodes[L]===void 0&&dt.records.groups[A]===void 0)throw new Error(`The right-hand id [${L}] does not yet exist. Please create the service/group before declaring an edge to it.`);const e=dt.records.nodes[A].in,l=dt.records.nodes[L].in;if(a&&e&&l&&e==l)throw new Error(`The left-hand id [${A}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(t&&e&&l&&e==l)throw new Error(`The right-hand id [${L}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const h={lhsId:A,lhsDir:b,lhsInto:g,lhsGroup:a,rhsId:L,rhsDir:T,rhsInto:i,rhsGroup:t,title:r};dt.records.edges.push(h),dt.records.nodes[A]&&dt.records.nodes[L]&&(dt.records.nodes[A].edges.push(dt.records.edges[dt.records.edges.length-1]),dt.records.nodes[L].edges.push(dt.records.edges[dt.records.edges.length-1]))},"addEdge"),wr=et(()=>dt.records.edges,"getEdges"),Lr=et(()=>{if(dt.records.dataStructures===void 0){const A=Object.entries(dt.records.nodes).reduce((a,[t,r])=>(a[t]=r.edges.reduce((e,l)=>{if(l.lhsId===t){const h=de(l.lhsDir,l.rhsDir);h&&(e[h]=l.rhsId)}else{const h=de(l.rhsDir,l.lhsDir);h&&(e[h]=l.lhsId)}return e},{}),a),{}),L=Object.keys(A)[0],b={[L]:1},T=Object.keys(A).reduce((a,t)=>t===L?a:{...a,[t]:1},{}),g=et(a=>{const t={[a]:[0,0]},r=[a];for(;r.length>0;){const e=r.shift();if(e){b[e]=1,delete T[e];const l=A[e],[h,d]=t[e];Object.entries(l).forEach(([o,c])=>{b[c]||(t[c]=lr([h,d],o),r.push(c))})}}return t},"BFS"),i=[g(L)];for(;Object.keys(T).length>0;)i.push(g(Object.keys(T)[0]));dt.records.dataStructures={adjList:A,spatialMaps:i}}return dt.records.dataStructures},"getDataStructures"),ie={clear:ur,setDiagramTitle:Ve,getDiagramTitle:ze,setAccTitle:Xe,getAccTitle:He,setAccDescription:ke,getAccDescription:Ye,addService:pr,getServices:fr,addJunction:mr,getJunctions:yr,getNodes:vr,getNode:Er,addGroup:Nr,getGroups:Tr,addEdge:Ar,getEdges:wr,setElementForId:et((A,L)=>{dt.records.elements[A]=L},"setElementForId"),getElementById:et(A=>dt.records.elements[A],"getElementById"),getDataStructures:Lr};function bt(A){const L=oe().architecture;return L!=null&&L[A]?L[A]:Oe[A]}et(bt,"getConfigField");var Ir=et((A,L)=>{Ke(A,L),A.groups.map(L.addGroup),A.services.map(b=>L.addService({...b,type:"service"})),A.junctions.map(b=>L.addJunction({...b,type:"junction"})),A.edges.map(L.addEdge)},"populateDb"),_r={parse:et(async A=>{const L=await tr("architecture",A);Ie.debug(L),Ir(L,ie)},"parse")},Mr=et(A=>`
  .edge {
    stroke-width: ${A.archEdgeWidth};
    stroke: ${A.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${A.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${A.archGroupBorderColor};
    stroke-width: ${A.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Kt=et(A=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${A}</g>`,"wrapIcon"),ee={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:Kt('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:Kt('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:Kt('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:Kt('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:Kt('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:Je,blank:{body:Kt("")}}},Cr=et(async function(A,L){const b=bt("padding"),T=bt("iconSize"),g=T/2,i=T/6,a=i/2;await Promise.all(L.edges().map(async t=>{var H,V;const{source:r,sourceDir:e,sourceArrow:l,sourceGroup:h,target:d,targetDir:o,targetArrow:c,targetGroup:n,label:y}=Ce(t);let{x:v,y:p}=t[0].sourceEndpoint();const{x:N,y:_}=t[0].midpoint();let{x:P,y:U}=t[0].targetEndpoint();const F=b+4;if(h&&(zt(e)?v+=e==="L"?-F:F:p+=e==="T"?-F:F+18),n&&(zt(o)?P+=o==="L"?-F:F:U+=o==="T"?-F:F+18),h||((H=ie.getNode(r))==null?void 0:H.type)!=="junction"||(zt(e)?v+=e==="L"?g:-g:p+=e==="T"?g:-g),n||((V=ie.getNode(d))==null?void 0:V.type)!=="junction"||(zt(o)?P+=o==="L"?g:-g:U+=o==="T"?g:-g),t[0]._private.rscratch){const x=A.insert("g");if(x.insert("path").attr("d",`M ${v},${p} L ${N},${_} L${P},${U} `).attr("class","edge"),l){const ot=zt(e)?ne[e](v,i):v-a,s=Bt(e)?ne[e](p,i):p-a;x.insert("polygon").attr("points",we[e](i)).attr("transform",`translate(${ot},${s})`).attr("class","arrow")}if(c){const ot=zt(o)?ne[o](P,i):P-a,s=Bt(o)?ne[o](U,i):U-a;x.insert("polygon").attr("points",we[o](i)).attr("transform",`translate(${ot},${s})`).attr("class","arrow")}if(y){const ot=Me(e,o)?"XY":zt(e)?"X":"Y";let s=0;s=ot==="X"?Math.abs(v-P):ot==="Y"?Math.abs(p-U)/1.5:Math.abs(v-P)/2;const E=x.append("g");if(await ce(E,y,{useHtmlLabels:!1,width:s,classes:"architecture-service-label"},oe()),E.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),ot==="X")E.attr("transform","translate("+N+", "+_+")");else if(ot==="Y")E.attr("transform","translate("+N+", "+_+") rotate(-90)");else if(ot==="XY"){const u=de(e,o);if(u&&sr(u)){const m=E.node().getBoundingClientRect(),[f,w]=dr(u);E.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*f*w*45})`);const C=E.node().getBoundingClientRect();E.attr("transform",`
                translate(${N}, ${_-m.height/2})
                translate(${f*C.width/2}, ${w*C.height/2})
                rotate(${-1*f*w*45}, 0, ${m.height/2})
              `)}}}}}))},"drawEdges"),Or=et(async function(A,L){const b=.75*bt("padding"),T=bt("fontSize"),g=bt("iconSize")/2;await Promise.all(L.nodes().map(async i=>{const a=Qt(i);if(a.type==="group"){const{h:t,w:r,x1:e,y1:l}=i.boundingBox();A.append("rect").attr("x",e+g).attr("y",l+g).attr("width",r).attr("height",t).attr("class","node-bkg");const h=A.append("g");let d=e,o=l;if(a.icon){const c=h.append("g");c.html(`<g>${await le(a.icon,{height:b,width:b,fallbackPrefix:ee.prefix})}</g>`),c.attr("transform","translate("+(d+g+1)+", "+(o+g+1)+")"),d+=b,o+=T/2-1-2}if(a.label){const c=h.append("g");await ce(c,a.label,{useHtmlLabels:!1,width:r,classes:"architecture-service-label"},oe()),c.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),c.attr("transform","translate("+(d+g+4)+", "+(o+g+2)+")")}}}))},"drawGroups"),xr=et(async function(A,L,b){for(const T of b){const g=L.append("g"),i=bt("iconSize");if(T.title){const e=g.append("g");await ce(e,T.title,{useHtmlLabels:!1,width:1.5*i,classes:"architecture-service-label"},oe()),e.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),e.attr("transform","translate("+i/2+", "+i+")")}const a=g.append("g");if(T.icon)a.html(`<g>${await le(T.icon,{height:i,width:i,fallbackPrefix:ee.prefix})}</g>`);else if(T.iconText){a.html(`<g>${await le("blank",{height:i,width:i,fallbackPrefix:ee.prefix})}</g>`);const e=a.append("g").append("foreignObject").attr("width",i).attr("height",i).append("div").attr("class","node-icon-text").attr("style",`height: ${i}px;`).append("div").html(T.iconText),l=parseInt(window.getComputedStyle(e.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;e.attr("style",`-webkit-line-clamp: ${Math.floor((i-2)/l)};`)}else a.append("path").attr("class","node-bkg").attr("id","node-"+T.id).attr("d",`M0 ${i} v${-i} q0,-5 5,-5 h${i} q5,0 5,5 v${i} H0 Z`);g.attr("class","architecture-service");const{width:t,height:r}=g._groups[0][0].getBBox();T.width=t,T.height=r,A.setElementForId(T.id,g)}return 0},"drawServices"),Dr=et(function(A,L,b){b.forEach(T=>{const g=L.append("g"),i=bt("iconSize");g.append("g").append("rect").attr("id","node-"+T.id).attr("fill-opacity","0").attr("width",i).attr("height",i),g.attr("class","architecture-junction");const{width:a,height:t}=g._groups[0][0].getBBox();g.width=a,g.height=t,A.setElementForId(T.id,g)})},"drawJunctions");function xe(A,L){A.forEach(b=>{L.add({group:"nodes",data:{type:"service",id:b.id,icon:b.icon,label:b.title,parent:b.in,width:bt("iconSize"),height:bt("iconSize")},classes:"node-service"})})}function De(A,L){A.forEach(b=>{L.add({group:"nodes",data:{type:"junction",id:b.id,parent:b.in,width:bt("iconSize"),height:bt("iconSize")},classes:"node-junction"})})}function Re(A,L){L.nodes().map(b=>{const T=Qt(b);T.type!=="group"&&(T.x=b.position().x,T.y=b.position().y,A.getElementById(T.id).attr("transform","translate("+(T.x||0)+","+(T.y||0)+")"))})}function be(A,L){A.forEach(b=>{L.add({group:"nodes",data:{type:"group",id:b.id,icon:b.icon,label:b.title,parent:b.in},classes:"node-group"})})}function Ge(A,L){A.forEach(b=>{const{lhsId:T,rhsId:g,lhsInto:i,lhsGroup:a,rhsInto:t,lhsDir:r,rhsDir:e,rhsGroup:l,title:h}=b,d=Me(b.lhsDir,b.rhsDir)?"segments":"straight",o={id:`${T}-${g}`,label:h,source:T,sourceDir:r,sourceArrow:i,sourceGroup:a,sourceEndpoint:r==="L"?"0 50%":r==="R"?"100% 50%":r==="T"?"50% 0":"50% 100%",target:g,targetDir:e,targetArrow:t,targetGroup:l,targetEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%"};L.add({group:"edges",data:o,classes:d})})}function Fe(A){const L=A.map(g=>{const i={},a={};return Object.entries(g).forEach(([t,[r,e]])=>{i[e]||(i[e]=[]),a[r]||(a[r]=[]),i[e].push(t),a[r].push(t)}),{horiz:Object.values(i).filter(t=>t.length>1),vert:Object.values(a).filter(t=>t.length>1)}}),[b,T]=L.reduce(([g,i],{horiz:a,vert:t})=>[[...g,...a],[...i,...t]],[[],[]]);return{horizontal:b,vertical:T}}function Se(A){const L=[],b=et(g=>`${g[0]},${g[1]}`,"posToStr"),T=et(g=>g.split(",").map(i=>parseInt(i)),"strToPos");return A.forEach(g=>{const i=Object.fromEntries(Object.entries(g).map(([e,l])=>[b(l),e])),a=[b([0,0])],t={},r={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;a.length>0;){const e=a.shift();if(e){t[e]=1;const l=i[e];if(l){const h=T(e);Object.entries(r).forEach(([d,o])=>{const c=b([h[0]+o[0],h[1]+o[1]]),n=i[c];n&&!t[c]&&(a.push(c),L.push({[Ae[d]]:n,[Ae[ar(d)]]:l,gap:1.5*bt("iconSize")}))})}}}}),L}function Pe(A,L,b,T,{spatialMaps:g}){return new Promise(i=>{const a=$e("body").append("div").attr("id","cy").attr("style","display:none"),t=_e({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${bt("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${bt("padding")}px`}}]});a.remove(),be(b,t),xe(A,t),De(L,t),Ge(T,t);const r=Fe(g),e=Se(g),l=t.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(h){const[d,o]=h.connectedNodes(),{parent:c}=Qt(d),{parent:n}=Qt(o);return c===n?1.5*bt("iconSize"):.5*bt("iconSize")},edgeElasticity(h){const[d,o]=h.connectedNodes(),{parent:c}=Qt(d),{parent:n}=Qt(o);return c===n?.45:.001},alignmentConstraint:r,relativePlacementConstraint:e});l.one("layoutstop",()=>{var d;function h(o,c,n,y){let v,p;const{x:N,y:_}=o,{x:P,y:U}=c;p=(y-_+(N-n)*(_-U)/(N-P))/Math.sqrt(1+Math.pow((_-U)/(N-P),2)),v=Math.sqrt(Math.pow(y-_,2)+Math.pow(n-N,2)-Math.pow(p,2)),v/=Math.sqrt(Math.pow(P-N,2)+Math.pow(U-_,2));let F=(P-N)*(y-_)-(U-_)*(n-N);switch(!0){case F>=0:F=1;break;case F<0:F=-1}let H=(P-N)*(n-N)+(U-_)*(y-_);switch(!0){case H>=0:H=1;break;case H<0:H=-1}return p=Math.abs(p)*F,v*=H,{distances:p,weights:v}}et(h,"getSegmentWeights"),t.startBatch();for(const o of Object.values(t.edges()))if((d=o.data)!=null&&d.call(o)){const{x:c,y:n}=o.source().position(),{x:y,y:v}=o.target().position();if(c!==y&&n!==v){const p=o.sourceEndpoint(),N=o.targetEndpoint(),{sourceDir:_}=Ce(o),[P,U]=Bt(_)?[p.x,N.y]:[N.x,p.y],{weights:F,distances:H}=h(p,N,P,U);o.style("segment-distances",H),o.style("segment-weights",F)}}t.endBatch(),l.run()}),l.run(),t.ready(h=>{Ie.info("Ready",h),i(t)})})}Ze([{name:ee.prefix,icons:ee}]),_e.use(or),et(xe,"addServices"),et(De,"addJunctions"),et(Re,"positionNodes"),et(be,"addGroups"),et(Ge,"addEdges"),et(Fe,"getAlignments"),et(Se,"getRelativeConstraints"),et(Pe,"layoutArchitecture");var Mn={parser:_r,db:ie,renderer:{draw:et(async(A,L,b,T)=>{const g=T.db,i=g.getServices(),a=g.getJunctions(),t=g.getGroups(),r=g.getEdges(),e=g.getDataStructures(),l=Be(L),h=l.append("g");h.attr("class","architecture-edges");const d=l.append("g");d.attr("class","architecture-services");const o=l.append("g");o.attr("class","architecture-groups"),await xr(g,d,i),Dr(g,d,a);const c=await Pe(i,a,t,r,e);await Cr(h,c),await Or(o,c),Re(g,c),We(void 0,l,bt("padding"),bt("useMaxWidth"))},"draw")},styles:Mr};export{Mn as diagram};
