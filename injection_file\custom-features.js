/**
 * 自定义功能模块 - 提供UI界面和用户交互
 * 基于success case简化实现
 */

const vscode = require('vscode');
const SimpleTokenManager = require('./token-manager');

class CustomFeatures {
    constructor() {
        this.context = null;
        this.tokenManager = null;
        this.webviewPanel = null;
        this.logger = {
            info: (msg) => console.log(`[CustomFeatures] ${msg}`),
            error: (msg, err) => console.error(`[CustomFeatures] ${msg}`, err),
            warn: (msg) => console.warn(`[CustomFeatures] ${msg}`)
        };
    }

    async initialize(context) {
        this.context = context;
        this.tokenManager = new SimpleTokenManager();
        await this.tokenManager.initialize(context);

        // 注册命令
        this.registerCommands();
        
        // 注册侧边栏视图
        this.registerWebviewProvider();

        this.logger.info('Custom Features initialized');
    }

    registerCommands() {
        // 注册一键更新命令
        const quickUpdateCommand = vscode.commands.registerCommand('simple.token.quickUpdate', async () => {
            const result = await this.tokenManager.quickUpdateToken();
            if (result.success) {
                vscode.window.showInformationMessage(result.message);
            } else {
                vscode.window.showErrorMessage(`更新失败: ${result.error}`);
            }
        });

        // 注册打开管理界面命令
        const openManagerCommand = vscode.commands.registerCommand('simple.token.openManager', () => {
            this.showTokenManager();
        });

        this.context.subscriptions.push(quickUpdateCommand, openManagerCommand);
    }

    registerWebviewProvider() {
        // 注册webview provider
        const provider = vscode.window.registerWebviewViewProvider('simpleTokenManager', {
            resolveWebviewView: (webviewView) => {
                webviewView.webview.options = {
                    enableScripts: true
                };
                webviewView.webview.html = this.getWebviewContent();
                
                // 处理webview消息
                webviewView.webview.onDidReceiveMessage(async (message) => {
                    await this.handleWebviewMessage(message, webviewView.webview);
                });
            }
        });

        this.context.subscriptions.push(provider);
    }

    async showTokenManager() {
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }

        this.webviewPanel = vscode.window.createWebviewPanel(
            'simpleTokenManager',
            'Simple Token Manager',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.webviewPanel.webview.html = this.getWebviewContent();

        // 处理webview消息
        this.webviewPanel.webview.onDidReceiveMessage(async (message) => {
            await this.handleWebviewMessage(message, this.webviewPanel.webview);
        });

        // 清理
        this.webviewPanel.onDidDispose(() => {
            this.webviewPanel = null;
        });
    }

    async handleWebviewMessage(message, webview) {
        try {
            switch (message.command) {
                case 'getTokens':
                    const tokensResult = await this.tokenManager.getAvailableTokens();
                    webview.postMessage({
                        command: 'tokensResult',
                        data: tokensResult
                    });
                    break;

                case 'getCurrentToken':
                    const currentResult = await this.tokenManager.getCurrentToken();
                    webview.postMessage({
                        command: 'currentTokenResult',
                        data: currentResult
                    });
                    break;

                case 'injectToken':
                    const injectResult = await this.tokenManager.injectToken(
                        message.accessToken,
                        message.tenantURL
                    );
                    webview.postMessage({
                        command: 'injectResult',
                        data: injectResult
                    });
                    if (injectResult.success) {
                        vscode.window.showInformationMessage('Token更新成功！');
                    }
                    break;

                case 'quickUpdate':
                    const quickResult = await this.tokenManager.quickUpdateToken();
                    webview.postMessage({
                        command: 'quickUpdateResult',
                        data: quickResult
                    });
                    if (quickResult.success) {
                        vscode.window.showInformationMessage(quickResult.message);
                    }
                    break;

                default:
                    this.logger.warn(`未知命令: ${message.command}`);
            }
        } catch (error) {
            this.logger.error('处理webview消息失败', error);
            webview.postMessage({
                command: 'error',
                data: { error: error.message }
            });
        }
    }

    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Token Manager</title>
    <style>
        body { 
            font-family: var(--vscode-font-family); 
            padding: 20px; 
            color: var(--vscode-foreground);
        }
        .section { 
            margin-bottom: 20px; 
            padding: 15px; 
            border: 1px solid var(--vscode-panel-border); 
            border-radius: 5px; 
        }
        .button { 
            background: var(--vscode-button-background); 
            color: var(--vscode-button-foreground); 
            border: none; 
            padding: 8px 16px; 
            margin: 5px; 
            border-radius: 3px; 
            cursor: pointer; 
        }
        .button:hover { 
            background: var(--vscode-button-hoverBackground); 
        }
        .token-item { 
            padding: 10px; 
            margin: 5px 0; 
            background: var(--vscode-editor-background); 
            border-radius: 3px; 
        }
        .current-token { 
            background: var(--vscode-textCodeBlock-background); 
            padding: 10px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 12px; 
        }
        .status { 
            margin: 10px 0; 
            padding: 8px; 
            border-radius: 3px; 
        }
        .success { 
            background: var(--vscode-testing-iconPassed); 
            color: white; 
        }
        .error { 
            background: var(--vscode-testing-iconFailed); 
            color: white; 
        }
    </style>
</head>
<body>
    <h2>🚀 Simple Token Manager</h2>
    
    <div class="section">
        <h3>快速操作</h3>
        <button class="button" onclick="quickUpdate()">🚀 一键更新Token</button>
        <button class="button" onclick="getCurrentToken()">📋 查看当前Token</button>
        <button class="button" onclick="getTokens()">🔄 刷新Token列表</button>
    </div>

    <div class="section">
        <h3>当前Token信息</h3>
        <div id="currentToken" class="current-token">点击"查看当前Token"获取信息</div>
    </div>

    <div class="section">
        <h3>可用Token列表</h3>
        <div id="tokenList">点击"刷新Token列表"获取可用token</div>
    </div>

    <div id="status"></div>

    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = \`<div class="status \${isError ? 'error' : 'success'}">\${message}</div>\`;
            setTimeout(() => status.innerHTML = '', 3000);
        }

        function quickUpdate() {
            vscode.postMessage({ command: 'quickUpdate' });
        }

        function getCurrentToken() {
            vscode.postMessage({ command: 'getCurrentToken' });
        }

        function getTokens() {
            vscode.postMessage({ command: 'getTokens' });
        }

        function injectToken(accessToken, tenantURL) {
            vscode.postMessage({ 
                command: 'injectToken', 
                accessToken: accessToken,
                tenantURL: tenantURL
            });
        }

        // 处理来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'currentTokenResult':
                    if (message.data.success) {
                        const token = message.data.accessToken;
                        const displayToken = token.length > 20 ? 
                            token.substring(0, 10) + '...' + token.substring(token.length - 10) : token;
                        document.getElementById('currentToken').innerHTML = \`
                            <strong>Access Token:</strong> \${displayToken}<br>
                            <strong>Tenant URL:</strong> \${message.data.tenantURL}
                        \`;
                    } else {
                        document.getElementById('currentToken').innerHTML = \`错误: \${message.data.error}\`;
                    }
                    break;

                case 'tokensResult':
                    if (message.data.success && message.data.tokens.length > 0) {
                        const tokenList = message.data.tokens.map((token, index) => \`
                            <div class="token-item">
                                <strong>Token \${index + 1}</strong><br>
                                ID: \${token.id || 'N/A'}<br>
                                <button class="button" onclick="injectToken('\${token.accessToken}', '\${token.tenantURL || ''}')">
                                    使用此Token
                                </button>
                            </div>
                        \`).join('');
                        document.getElementById('tokenList').innerHTML = tokenList;
                    } else {
                        document.getElementById('tokenList').innerHTML = \`<div class="error">获取token失败: \${message.data.error || '无可用token'}</div>\`;
                    }
                    break;

                case 'injectResult':
                    if (message.data.success) {
                        showStatus('Token注入成功！');
                        getCurrentToken(); // 刷新当前token显示
                    } else {
                        showStatus(\`注入失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'quickUpdateResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                        getCurrentToken(); // 刷新当前token显示
                    } else {
                        showStatus(\`快速更新失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'error':
                    showStatus(\`错误: \${message.data.error}\`, true);
                    break;
            }
        });

        // 页面加载时获取当前token信息
        getCurrentToken();
    </script>
</body>
</html>`;
    }

    dispose() {
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
        }
        if (this.tokenManager) {
            this.tokenManager.dispose();
        }
        this.logger.info('Custom Features disposed');
    }
}

module.exports = CustomFeatures;
