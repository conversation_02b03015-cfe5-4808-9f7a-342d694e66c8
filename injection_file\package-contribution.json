{"contributes": {"commands": [{"command": "simple.token.quickUpdate", "title": "Quick Update Token", "category": "Simple Token"}, {"command": "simple.token.openManager", "title": "Open Token Manager", "category": "Simple Token"}], "views": {"explorer": [{"id": "simpleTokenManager", "name": "Simple Token Manager", "when": "true"}]}, "viewsWelcome": [{"view": "simpleTokenManager", "contents": "Welcome to Simple Token Manager\n[Quick Update](command:simple.token.quickUpdate)\n[Open Manager](command:simple.token.openManager)"}]}}