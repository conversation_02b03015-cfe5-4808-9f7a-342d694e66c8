# 🌐 Backend API 规范文档

## 📋 概述

本文档定义了 Simple Token Manager 所需的后端 API 规范。你需要实现一个简单的 HTTP API 服务，提供 token 管理功能。

## 🔧 基础配置

### 认证方式
- **类型**: Bearer Token 认证
- **Header**: `Authorization: Bearer {password}`
- **密码**: 在 `token-manager.js` 中配置的 `AUTH_PASSWORD`

### 请求头要求
```http
Authorization: Bearer your_secret_password
Content-Type: application/json
User-Agent: VSCode-SimpleTokenManager/1.0.0
```

## 📡 API 端点规范

### 1. 获取可用 Token 列表

#### 请求
```http
GET /api/tokens
Authorization: Bearer your_secret_password
Content-Type: application/json
User-Agent: VSCode-SimpleTokenManager/1.0.0
```

#### 响应格式
```json
{
  "success": true,
  "tokens": [
    {
      "id": "token_001",
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "tenantURL": "https://d5.api.augmentcode.com/",
      "description": "Token 1 - 用户A",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": "token_002", 
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "tenantURL": "https://d5.api.augmentcode.com/",
      "description": "Token 2 - 用户B",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 2,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 字段说明
- `success`: 布尔值，表示请求是否成功
- `tokens`: Token 数组
  - `id`: Token 唯一标识符（字符串）
  - `accessToken`: 实际的 JWT token 字符串（必需）
  - `tenantURL`: 租户 URL，默认为 `https://d5.api.augmentcode.com/`（可选）
  - `description`: Token 描述信息（可选）
  - `createdAt`: 创建时间（可选）
- `count`: Token 数量（可选）
- `timestamp`: 响应时间戳（可选）

#### 错误响应
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Invalid authentication token"
}
```

## 🔒 认证流程

### 1. 密码验证
- 客户端发送 `Authorization: Bearer {password}`
- 服务端验证密码是否匹配
- 匹配则返回 token 列表，否则返回 401 错误

### 2. 安全建议
- 使用强密码
- 考虑添加 IP 白名单
- 记录访问日志
- 定期轮换密码

## 📊 响应状态码

| 状态码 | 含义 | 场景 |
|--------|------|------|
| 200 | 成功 | 正常返回 token 列表 |
| 401 | 未授权 | 密码错误或缺失 |
| 403 | 禁止访问 | IP 被限制等 |
| 404 | 未找到 | 端点不存在 |
| 500 | 服务器错误 | 内部错误 |

## 🧪 测试示例

### 使用 curl 测试
```bash
# 测试获取 token 列表
curl -X GET \
  -H "Authorization: Bearer your_secret_password" \
  -H "Content-Type: application/json" \
  -H "User-Agent: VSCode-SimpleTokenManager/1.0.0" \
  http://localhost:3000/api/tokens
```

### 预期响应
```json
{
  "success": true,
  "tokens": [
    {
      "id": "test_token_1",
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
      "tenantURL": "https://d5.api.augmentcode.com/",
      "description": "测试 Token"
    }
  ]
}
```

## 🔧 实现建议

### 最小实现
1. **HTTP 服务器**: Express.js、FastAPI、Go Gin 等
2. **认证中间件**: 验证 Bearer token
3. **Token 存储**: 文件、数据库或内存
4. **CORS 支持**: 如果需要跨域访问

### 数据存储
- **简单方案**: JSON 文件或内存数组
- **持久化方案**: SQLite、PostgreSQL、MySQL
- **云方案**: Firebase、Supabase

### 示例伪代码
```javascript
// Express.js 示例
app.get('/api/tokens', authenticateToken, (req, res) => {
  const tokens = getTokensFromStorage();
  res.json({
    success: true,
    tokens: tokens,
    count: tokens.length,
    timestamp: new Date().toISOString()
  });
});

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token !== process.env.AUTH_PASSWORD) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized'
    });
  }
  
  next();
}
```

## 🚀 部署建议

### 本地开发
- 使用 `http://localhost:3000`
- 在 `token-manager.js` 中配置对应地址

### 生产环境
- 使用 HTTPS
- 配置防火墙
- 使用环境变量管理密码
- 添加监控和日志

## ⚠️ 注意事项

1. **Token 格式**: 确保 `accessToken` 是有效的 JWT 格式
2. **tenantURL**: 如果不提供，会使用默认值
3. **数组格式**: `tokens` 必须是数组，即使为空也要返回 `[]`
4. **错误处理**: 所有错误都应返回 JSON 格式
5. **超时**: 客户端有 10 秒超时，确保响应及时

## 📞 调试技巧

1. **日志记录**: 记录所有请求和响应
2. **状态检查**: 提供健康检查端点 `/health`
3. **测试工具**: 使用 Postman 或 curl 测试
4. **错误信息**: 提供详细的错误描述
