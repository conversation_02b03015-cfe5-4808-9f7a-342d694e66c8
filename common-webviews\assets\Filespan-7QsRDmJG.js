import{c as R,E as U,n as V,p as Z,r as aa,ac as sa,ad as na,ae as ea,af as la,ag as ta,ah as G,z as ra,A as ca,C as f,X as d,$ as ia,D as oa,T as pa,F as H,G as C,b as u,I as va,a as fa,P as ua,J as w,H as E,L as J,M as k,Q as K,W as L,_ as i,m as h,t as m,N as M,a3 as da,ab as ha,Y as g,K as P}from"./SpinnerAugment-BY2Lraps.js";import{n as ma,g as ga,a as _a}from"./focusTrapStack-CAuiPHBF.js";function ba(_,s,b,r,I,$){var o,p,l,t=null,z=_;R(()=>{const e=s()||null;var c=e==="svg"?na:null;e!==o&&(l&&(e===null?Z(l,()=>{l=null,p=null}):e===p?aa(l):(sa(l),G(!1))),e&&e!==p&&(l=V(()=>{if(t=c?document.createElementNS(c,e):document.createElement(e),ea(t,t),r){var x=t.appendChild(la());r(t,x)}ta.nodes_end=t,z.before(t)})),(o=e)&&(p=o),G(!0))},U)}var Ia=w('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),$a=w('<span class="right-icons svelte-9pfhnp"><!></span>'),za=w('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function ka(_,s){const b=ra(s);ca(s,!1);const r=h(),I=h(),$=h(),o=h();let p=f(s,"class",8,""),l=f(s,"filepath",8),t=f(s,"size",8,1),z=f(s,"nopath",8,!1),e=f(s,"growname",8,!0),c=f(s,"onClick",24,()=>{});d(()=>P(l()),()=>{g(r,ma(l()))}),d(()=>i(r),()=>{g(I,ga(i(r)))}),d(()=>i(r),()=>{g($,_a(i(r)))}),d(()=>P(c()),()=>{g(o,c()?"button":"div")}),ia(),oa(),pa(_,{get size(){return t()},children:(x,xa)=>{var F=H();ba(C(F),()=>i(o),0,(N,Q)=>{fa(N,()=>({class:`c-filespan ${p()}`,role:c()?"button":"",tabindex:"0"}),void 0,"svelte-9pfhnp"),ua("click",N,function(...a){var n;(n=c())==null||n.apply(this,a)});var X=za(),y=C(X),S=a=>{var n=H(),v=C(n);M(v,s,"leftIcon",{},null),u(a,n)};E(y,a=>{J(()=>b.leftIcon)&&a(S)});var A=k(y,2),T=m(A),D=k(A,2),W=a=>{var n=Ia();let v;var q=m(n),B=m(q);K(O=>{v=ha(n,1,"c-filespan__dir svelte-9pfhnp",null,v,O),L(B,i($))},[()=>({growname:e()})],da),u(a,n)};E(D,a=>{z()||a(W)});var j=k(D,2),Y=a=>{var n=$a(),v=m(n);M(v,s,"rightIcon",{},null),u(a,n)};E(j,a=>{J(()=>b.rightIcon)&&a(Y)}),K(()=>L(T,i(I))),u(Q,X)}),u(x,F)},$$slots:{default:!0}}),va()}export{ka as F,ba as e};
