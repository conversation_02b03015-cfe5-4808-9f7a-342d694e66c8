class p0{constructor(e,r,a){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=r,this.end=a}static range(e,r){return r?e&&e.loc&&r.loc&&e.loc.lexer===r.loc.lexer?new p0(e.loc.lexer,e.loc.start,r.loc.end):null:e&&e.loc}}class g0{constructor(e,r){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=r}range(e,r){return new g0(r,p0.range(this,e))}}class M{constructor(e,r){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var a,n,s="KaTeX parse error: "+e,h=r&&r.loc;if(h&&h.start<=h.end){var c=h.lexer.input;a=h.start,n=h.end,a===c.length?s+=" at end of input: ":s+=" at position "+(a+1)+": ";var p=c.slice(a,n).replace(/[^]/g,"$&̲");s+=(a>15?"…"+c.slice(a-15,a):c.slice(0,a))+p+(n+15<c.length?c.slice(n,n+15)+"…":c.slice(n))}var g=new Error(s);return g.name="ParseError",g.__proto__=M.prototype,g.position=a,a!=null&&n!=null&&(g.length=n-a),g.rawMessage=e,g}}M.prototype.__proto__=Error.prototype;var e1=/([A-Z])/g,t1={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},r1=/[&><"']/g,St=function t(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?t(e.body[0]):e:e.type==="font"?t(e.body):e},R={contains:function(t,e){return t.indexOf(e)!==-1},deflt:function(t,e){return t===void 0?e:t},escape:function(t){return String(t).replace(r1,e=>t1[e])},hyphenate:function(t){return t.replace(e1,"-$1").toLowerCase()},getBaseElem:St,isCharacterBox:function(t){var e=St(t);return e.type==="mathord"||e.type==="textord"||e.type==="atom"},protocolFromUrl:function(t){var e=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(t);return e?e[2]!==":"?null:/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(e[1])?e[1].toLowerCase():null:"_relative"}},xe={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:t=>"#"+t},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(t,e)=>(e.push(t),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:t=>Math.max(0,t),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:t=>Math.max(0,t),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:t=>Math.max(0,t),cli:"-e, --max-expand <n>",cliProcessor:t=>t==="Infinity"?1/0:parseInt(t)},globalGroup:{type:"boolean",cli:!1}};function a1(t){if(t.default)return t.default;var e=t.type,r=Array.isArray(e)?e[0]:e;if(typeof r!="string")return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class ut{constructor(e){for(var r in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},xe)if(xe.hasOwnProperty(r)){var a=xe[r];this[r]=e[r]!==void 0?a.processor?a.processor(e[r]):e[r]:a1(a)}}reportNonstrict(e,r,a){var n=this.strict;if(typeof n=="function"&&(n=n(e,r,a)),n&&n!=="ignore"){if(n===!0||n==="error")throw new M("LaTeX-incompatible input and strict mode is set to 'error': "+r+" ["+e+"]",a);n==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+r+" ["+e+"]"):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+r+" ["+e+"]")}}useStrictBehavior(e,r,a){var n=this.strict;if(typeof n=="function")try{n=n(e,r,a)}catch{n="error"}return!(!n||n==="ignore")&&(n===!0||n==="error"||(n==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+r+" ["+e+"]"),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+r+" ["+e+"]"),!1)))}isTrusted(e){if(e.url&&!e.protocol){var r=R.protocolFromUrl(e.url);if(r==null)return!1;e.protocol=r}var a=typeof this.trust=="function"?this.trust(e):this.trust;return!!a}}class D0{constructor(e,r,a){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=r,this.cramped=a}sup(){return k0[n1[this.id]]}sub(){return k0[i1[this.id]]}fracNum(){return k0[o1[this.id]]}fracDen(){return k0[s1[this.id]]}cramp(){return k0[l1[this.id]]}text(){return k0[h1[this.id]]}isTight(){return this.size>=2}}var k0=[new D0(0,0,!1),new D0(1,0,!0),new D0(2,1,!1),new D0(3,1,!0),new D0(4,2,!1),new D0(5,2,!0),new D0(6,3,!1),new D0(7,3,!0)],n1=[4,5,4,5,6,7,6,7],i1=[5,5,5,5,7,7,7,7],o1=[2,3,4,5,6,7,6,7],s1=[3,3,5,5,7,7,7,7],l1=[1,1,3,3,5,5,7,7],h1=[0,1,2,3,2,3,2,3],I={DISPLAY:k0[0],TEXT:k0[2],SCRIPT:k0[4],SCRIPTSCRIPT:k0[6]},rt=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}],we=[];function vr(t){for(var e=0;e<we.length;e+=2)if(t>=we[e]&&t<=we[e+1])return!0;return!1}rt.forEach(t=>t.blocks.forEach(e=>we.push(...e)));var $0=80,Mt={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`};class re{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return R.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){for(var e="",r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e}toText(){return this.children.map(e=>e.toText()).join("")}}var S0={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},he={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},zt={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function m1(t,e){S0[t]=e}function dt(t,e,r){if(!S0[e])throw new Error("Font metrics not found for font: "+e+".");var a=t.charCodeAt(0),n=S0[e][a];if(!n&&t[0]in zt&&(a=zt[t[0]].charCodeAt(0),n=S0[e][a]),n||r!=="text"||vr(a)&&(n=S0[e][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var Oe={},c1=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],At=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Tt=function(t,e){return e.size<2?t:c1[t-1][e.size-1]};class N0{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||N0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=At[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var r={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var a in e)e.hasOwnProperty(a)&&(r[a]=e[a]);return new N0(r)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:Tt(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:At[e-1]})}havingBaseStyle(e){e=e||this.style.text();var r=Tt(N0.BASESIZE,e);return this.size===r&&this.textSize===N0.BASESIZE&&this.style===e?this:this.extend({style:e,size:r})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==N0.BASESIZE?["sizing","reset-size"+this.size,"size"+N0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){var r;if(!Oe[r=e>=5?0:e>=3?1:2]){var a=Oe[r]={cssEmPerMu:he.quad[r]/18};for(var n in he)he.hasOwnProperty(n)&&(a[n]=he[n][r])}return Oe[r]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}N0.BASESIZE=6;var at={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},p1={ex:!0,em:!0,mu:!0},br=function(t){return typeof t!="string"&&(t=t.unit),t in at||t in p1||t==="ex"},Q=function(t,e){var r;if(t.unit in at)r=at[t.unit]/e.fontMetrics().ptPerEm/e.sizeMultiplier;else if(t.unit==="mu")r=e.fontMetrics().cssEmPerMu;else{var a;if(a=e.style.isTight()?e.havingStyle(e.style.text()):e,t.unit==="ex")r=a.fontMetrics().xHeight;else{if(t.unit!=="em")throw new M("Invalid unit: '"+t.unit+"'");r=a.fontMetrics().quad}a!==e&&(r*=a.sizeMultiplier/e.sizeMultiplier)}return Math.min(t.number*r,e.maxSize)},A=function(t){return+t.toFixed(4)+"em"},U0=function(t){return t.filter(e=>e).join(" ")},yr=function(t,e,r){if(this.classes=t||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},e){e.style.isTight()&&this.classes.push("mtight");var a=e.getColor();a&&(this.style.color=a)}},xr=function(t){var e=document.createElement(t);for(var r in e.className=U0(this.classes),this.style)this.style.hasOwnProperty(r)&&(e.style[r]=this.style[r]);for(var a in this.attributes)this.attributes.hasOwnProperty(a)&&e.setAttribute(a,this.attributes[a]);for(var n=0;n<this.children.length;n++)e.appendChild(this.children[n].toNode());return e},u1=/[\s"'>/=\x00-\x1f]/,wr=function(t){var e="<"+t;this.classes.length&&(e+=' class="'+R.escape(U0(this.classes))+'"');var r="";for(var a in this.style)this.style.hasOwnProperty(a)&&(r+=R.hyphenate(a)+":"+this.style[a]+";");for(var n in r&&(e+=' style="'+R.escape(r)+'"'),this.attributes)if(this.attributes.hasOwnProperty(n)){if(u1.test(n))throw new M("Invalid attribute name '"+n+"'");e+=" "+n+'="'+R.escape(this.attributes[n])+'"'}e+=">";for(var s=0;s<this.children.length;s++)e+=this.children[s].toMarkup();return e+="</"+t+">"};class ae{constructor(e,r,a,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,yr.call(this,e,a,n),this.children=r||[]}setAttribute(e,r){this.attributes[e]=r}hasClass(e){return R.contains(this.classes,e)}toNode(){return xr.call(this,"span")}toMarkup(){return wr.call(this,"span")}}class gt{constructor(e,r,a,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,yr.call(this,r,n),this.children=a||[],this.setAttribute("href",e)}setAttribute(e,r){this.attributes[e]=r}hasClass(e){return R.contains(this.classes,e)}toNode(){return xr.call(this,"a")}toMarkup(){return wr.call(this,"a")}}class d1{constructor(e,r,a){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=r,this.src=e,this.classes=["mord"],this.style=a}hasClass(e){return R.contains(this.classes,e)}toNode(){var e=document.createElement("img");for(var r in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(r)&&(e.style[r]=this.style[r]);return e}toMarkup(){var e='<img src="'+R.escape(this.src)+'" alt="'+R.escape(this.alt)+'"',r="";for(var a in this.style)this.style.hasOwnProperty(a)&&(r+=R.hyphenate(a)+":"+this.style[a]+";");return r&&(e+=' style="'+R.escape(r)+'"'),e+="'/>"}}var g1={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class f0{constructor(e,r,a,n,s,h,c,p){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=r||0,this.depth=a||0,this.italic=n||0,this.skew=s||0,this.width=h||0,this.classes=c||[],this.style=p||{},this.maxFontSize=0;var g=function(b){for(var x=0;x<rt.length;x++)for(var v=rt[x],w=0;w<v.blocks.length;w++){var z=v.blocks[w];if(b>=z[0]&&b<=z[1])return v.name}return null}(this.text.charCodeAt(0));g&&this.classes.push(g+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=g1[this.text])}hasClass(e){return R.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),r=null;for(var a in this.italic>0&&((r=document.createElement("span")).style.marginRight=A(this.italic)),this.classes.length>0&&((r=r||document.createElement("span")).className=U0(this.classes)),this.style)this.style.hasOwnProperty(a)&&((r=r||document.createElement("span")).style[a]=this.style[a]);return r?(r.appendChild(e),r):e}toMarkup(){var e=!1,r="<span";this.classes.length&&(e=!0,r+=' class="',r+=R.escape(U0(this.classes)),r+='"');var a="";for(var n in this.italic>0&&(a+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(n)&&(a+=R.hyphenate(n)+":"+this.style[n]+";");a&&(e=!0,r+=' style="'+R.escape(a)+'"');var s=R.escape(this.text);return e?(r+=">",r+=s,r+="</span>"):s}}class H0{constructor(e,r){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=r||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&e.setAttribute(r,this.attributes[r]);for(var a=0;a<this.children.length;a++)e.appendChild(this.children[a].toNode());return e}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="'+R.escape(this.attributes[r])+'"');e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</svg>"}}class Y0{constructor(e,r){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=r}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",Mt[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+R.escape(this.alternate)+'"/>':'<path d="'+R.escape(Mt[this.pathName])+'"/>'}}class nt{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&e.setAttribute(r,this.attributes[r]);return e}toMarkup(){var e="<line";for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="'+R.escape(this.attributes[r])+'"');return e+="/>"}}function Bt(t){if(t instanceof f0)return t;throw new Error("Expected symbolNode but got "+String(t)+".")}var f1={bin:1,close:1,inner:1,open:1,punct:1,rel:1},v1={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},$={math:{},text:{}};function i(t,e,r,a,n,s){$[t][n]={font:e,group:r,replace:a},s&&a&&($[t][a]=$[t][n])}var o="math",k="text",l="main",u="ams",Z="accent-token",C="bin",s0="close",Z0="inner",H="mathord",r0="op-token",c0="open",me="punct",d="rel",C0="spacing",f="textord";i(o,l,d,"≡","\\equiv",!0),i(o,l,d,"≺","\\prec",!0),i(o,l,d,"≻","\\succ",!0),i(o,l,d,"∼","\\sim",!0),i(o,l,d,"⊥","\\perp"),i(o,l,d,"⪯","\\preceq",!0),i(o,l,d,"⪰","\\succeq",!0),i(o,l,d,"≃","\\simeq",!0),i(o,l,d,"∣","\\mid",!0),i(o,l,d,"≪","\\ll",!0),i(o,l,d,"≫","\\gg",!0),i(o,l,d,"≍","\\asymp",!0),i(o,l,d,"∥","\\parallel"),i(o,l,d,"⋈","\\bowtie",!0),i(o,l,d,"⌣","\\smile",!0),i(o,l,d,"⊑","\\sqsubseteq",!0),i(o,l,d,"⊒","\\sqsupseteq",!0),i(o,l,d,"≐","\\doteq",!0),i(o,l,d,"⌢","\\frown",!0),i(o,l,d,"∋","\\ni",!0),i(o,l,d,"∝","\\propto",!0),i(o,l,d,"⊢","\\vdash",!0),i(o,l,d,"⊣","\\dashv",!0),i(o,l,d,"∋","\\owns"),i(o,l,me,".","\\ldotp"),i(o,l,me,"⋅","\\cdotp"),i(o,l,f,"#","\\#"),i(k,l,f,"#","\\#"),i(o,l,f,"&","\\&"),i(k,l,f,"&","\\&"),i(o,l,f,"ℵ","\\aleph",!0),i(o,l,f,"∀","\\forall",!0),i(o,l,f,"ℏ","\\hbar",!0),i(o,l,f,"∃","\\exists",!0),i(o,l,f,"∇","\\nabla",!0),i(o,l,f,"♭","\\flat",!0),i(o,l,f,"ℓ","\\ell",!0),i(o,l,f,"♮","\\natural",!0),i(o,l,f,"♣","\\clubsuit",!0),i(o,l,f,"℘","\\wp",!0),i(o,l,f,"♯","\\sharp",!0),i(o,l,f,"♢","\\diamondsuit",!0),i(o,l,f,"ℜ","\\Re",!0),i(o,l,f,"♡","\\heartsuit",!0),i(o,l,f,"ℑ","\\Im",!0),i(o,l,f,"♠","\\spadesuit",!0),i(o,l,f,"§","\\S",!0),i(k,l,f,"§","\\S"),i(o,l,f,"¶","\\P",!0),i(k,l,f,"¶","\\P"),i(o,l,f,"†","\\dag"),i(k,l,f,"†","\\dag"),i(k,l,f,"†","\\textdagger"),i(o,l,f,"‡","\\ddag"),i(k,l,f,"‡","\\ddag"),i(k,l,f,"‡","\\textdaggerdbl"),i(o,l,s0,"⎱","\\rmoustache",!0),i(o,l,c0,"⎰","\\lmoustache",!0),i(o,l,s0,"⟯","\\rgroup",!0),i(o,l,c0,"⟮","\\lgroup",!0),i(o,l,C,"∓","\\mp",!0),i(o,l,C,"⊖","\\ominus",!0),i(o,l,C,"⊎","\\uplus",!0),i(o,l,C,"⊓","\\sqcap",!0),i(o,l,C,"∗","\\ast"),i(o,l,C,"⊔","\\sqcup",!0),i(o,l,C,"◯","\\bigcirc",!0),i(o,l,C,"∙","\\bullet",!0),i(o,l,C,"‡","\\ddagger"),i(o,l,C,"≀","\\wr",!0),i(o,l,C,"⨿","\\amalg"),i(o,l,C,"&","\\And"),i(o,l,d,"⟵","\\longleftarrow",!0),i(o,l,d,"⇐","\\Leftarrow",!0),i(o,l,d,"⟸","\\Longleftarrow",!0),i(o,l,d,"⟶","\\longrightarrow",!0),i(o,l,d,"⇒","\\Rightarrow",!0),i(o,l,d,"⟹","\\Longrightarrow",!0),i(o,l,d,"↔","\\leftrightarrow",!0),i(o,l,d,"⟷","\\longleftrightarrow",!0),i(o,l,d,"⇔","\\Leftrightarrow",!0),i(o,l,d,"⟺","\\Longleftrightarrow",!0),i(o,l,d,"↦","\\mapsto",!0),i(o,l,d,"⟼","\\longmapsto",!0),i(o,l,d,"↗","\\nearrow",!0),i(o,l,d,"↩","\\hookleftarrow",!0),i(o,l,d,"↪","\\hookrightarrow",!0),i(o,l,d,"↘","\\searrow",!0),i(o,l,d,"↼","\\leftharpoonup",!0),i(o,l,d,"⇀","\\rightharpoonup",!0),i(o,l,d,"↙","\\swarrow",!0),i(o,l,d,"↽","\\leftharpoondown",!0),i(o,l,d,"⇁","\\rightharpoondown",!0),i(o,l,d,"↖","\\nwarrow",!0),i(o,l,d,"⇌","\\rightleftharpoons",!0),i(o,u,d,"≮","\\nless",!0),i(o,u,d,"","\\@nleqslant"),i(o,u,d,"","\\@nleqq"),i(o,u,d,"⪇","\\lneq",!0),i(o,u,d,"≨","\\lneqq",!0),i(o,u,d,"","\\@lvertneqq"),i(o,u,d,"⋦","\\lnsim",!0),i(o,u,d,"⪉","\\lnapprox",!0),i(o,u,d,"⊀","\\nprec",!0),i(o,u,d,"⋠","\\npreceq",!0),i(o,u,d,"⋨","\\precnsim",!0),i(o,u,d,"⪹","\\precnapprox",!0),i(o,u,d,"≁","\\nsim",!0),i(o,u,d,"","\\@nshortmid"),i(o,u,d,"∤","\\nmid",!0),i(o,u,d,"⊬","\\nvdash",!0),i(o,u,d,"⊭","\\nvDash",!0),i(o,u,d,"⋪","\\ntriangleleft"),i(o,u,d,"⋬","\\ntrianglelefteq",!0),i(o,u,d,"⊊","\\subsetneq",!0),i(o,u,d,"","\\@varsubsetneq"),i(o,u,d,"⫋","\\subsetneqq",!0),i(o,u,d,"","\\@varsubsetneqq"),i(o,u,d,"≯","\\ngtr",!0),i(o,u,d,"","\\@ngeqslant"),i(o,u,d,"","\\@ngeqq"),i(o,u,d,"⪈","\\gneq",!0),i(o,u,d,"≩","\\gneqq",!0),i(o,u,d,"","\\@gvertneqq"),i(o,u,d,"⋧","\\gnsim",!0),i(o,u,d,"⪊","\\gnapprox",!0),i(o,u,d,"⊁","\\nsucc",!0),i(o,u,d,"⋡","\\nsucceq",!0),i(o,u,d,"⋩","\\succnsim",!0),i(o,u,d,"⪺","\\succnapprox",!0),i(o,u,d,"≆","\\ncong",!0),i(o,u,d,"","\\@nshortparallel"),i(o,u,d,"∦","\\nparallel",!0),i(o,u,d,"⊯","\\nVDash",!0),i(o,u,d,"⋫","\\ntriangleright"),i(o,u,d,"⋭","\\ntrianglerighteq",!0),i(o,u,d,"","\\@nsupseteqq"),i(o,u,d,"⊋","\\supsetneq",!0),i(o,u,d,"","\\@varsupsetneq"),i(o,u,d,"⫌","\\supsetneqq",!0),i(o,u,d,"","\\@varsupsetneqq"),i(o,u,d,"⊮","\\nVdash",!0),i(o,u,d,"⪵","\\precneqq",!0),i(o,u,d,"⪶","\\succneqq",!0),i(o,u,d,"","\\@nsubseteqq"),i(o,u,C,"⊴","\\unlhd"),i(o,u,C,"⊵","\\unrhd"),i(o,u,d,"↚","\\nleftarrow",!0),i(o,u,d,"↛","\\nrightarrow",!0),i(o,u,d,"⇍","\\nLeftarrow",!0),i(o,u,d,"⇏","\\nRightarrow",!0),i(o,u,d,"↮","\\nleftrightarrow",!0),i(o,u,d,"⇎","\\nLeftrightarrow",!0),i(o,u,d,"△","\\vartriangle"),i(o,u,f,"ℏ","\\hslash"),i(o,u,f,"▽","\\triangledown"),i(o,u,f,"◊","\\lozenge"),i(o,u,f,"Ⓢ","\\circledS"),i(o,u,f,"®","\\circledR"),i(k,u,f,"®","\\circledR"),i(o,u,f,"∡","\\measuredangle",!0),i(o,u,f,"∄","\\nexists"),i(o,u,f,"℧","\\mho"),i(o,u,f,"Ⅎ","\\Finv",!0),i(o,u,f,"⅁","\\Game",!0),i(o,u,f,"‵","\\backprime"),i(o,u,f,"▲","\\blacktriangle"),i(o,u,f,"▼","\\blacktriangledown"),i(o,u,f,"■","\\blacksquare"),i(o,u,f,"⧫","\\blacklozenge"),i(o,u,f,"★","\\bigstar"),i(o,u,f,"∢","\\sphericalangle",!0),i(o,u,f,"∁","\\complement",!0),i(o,u,f,"ð","\\eth",!0),i(k,l,f,"ð","ð"),i(o,u,f,"╱","\\diagup"),i(o,u,f,"╲","\\diagdown"),i(o,u,f,"□","\\square"),i(o,u,f,"□","\\Box"),i(o,u,f,"◊","\\Diamond"),i(o,u,f,"¥","\\yen",!0),i(k,u,f,"¥","\\yen",!0),i(o,u,f,"✓","\\checkmark",!0),i(k,u,f,"✓","\\checkmark"),i(o,u,f,"ℶ","\\beth",!0),i(o,u,f,"ℸ","\\daleth",!0),i(o,u,f,"ℷ","\\gimel",!0),i(o,u,f,"ϝ","\\digamma",!0),i(o,u,f,"ϰ","\\varkappa"),i(o,u,c0,"┌","\\@ulcorner",!0),i(o,u,s0,"┐","\\@urcorner",!0),i(o,u,c0,"└","\\@llcorner",!0),i(o,u,s0,"┘","\\@lrcorner",!0),i(o,u,d,"≦","\\leqq",!0),i(o,u,d,"⩽","\\leqslant",!0),i(o,u,d,"⪕","\\eqslantless",!0),i(o,u,d,"≲","\\lesssim",!0),i(o,u,d,"⪅","\\lessapprox",!0),i(o,u,d,"≊","\\approxeq",!0),i(o,u,C,"⋖","\\lessdot"),i(o,u,d,"⋘","\\lll",!0),i(o,u,d,"≶","\\lessgtr",!0),i(o,u,d,"⋚","\\lesseqgtr",!0),i(o,u,d,"⪋","\\lesseqqgtr",!0),i(o,u,d,"≑","\\doteqdot"),i(o,u,d,"≓","\\risingdotseq",!0),i(o,u,d,"≒","\\fallingdotseq",!0),i(o,u,d,"∽","\\backsim",!0),i(o,u,d,"⋍","\\backsimeq",!0),i(o,u,d,"⫅","\\subseteqq",!0),i(o,u,d,"⋐","\\Subset",!0),i(o,u,d,"⊏","\\sqsubset",!0),i(o,u,d,"≼","\\preccurlyeq",!0),i(o,u,d,"⋞","\\curlyeqprec",!0),i(o,u,d,"≾","\\precsim",!0),i(o,u,d,"⪷","\\precapprox",!0),i(o,u,d,"⊲","\\vartriangleleft"),i(o,u,d,"⊴","\\trianglelefteq"),i(o,u,d,"⊨","\\vDash",!0),i(o,u,d,"⊪","\\Vvdash",!0),i(o,u,d,"⌣","\\smallsmile"),i(o,u,d,"⌢","\\smallfrown"),i(o,u,d,"≏","\\bumpeq",!0),i(o,u,d,"≎","\\Bumpeq",!0),i(o,u,d,"≧","\\geqq",!0),i(o,u,d,"⩾","\\geqslant",!0),i(o,u,d,"⪖","\\eqslantgtr",!0),i(o,u,d,"≳","\\gtrsim",!0),i(o,u,d,"⪆","\\gtrapprox",!0),i(o,u,C,"⋗","\\gtrdot"),i(o,u,d,"⋙","\\ggg",!0),i(o,u,d,"≷","\\gtrless",!0),i(o,u,d,"⋛","\\gtreqless",!0),i(o,u,d,"⪌","\\gtreqqless",!0),i(o,u,d,"≖","\\eqcirc",!0),i(o,u,d,"≗","\\circeq",!0),i(o,u,d,"≜","\\triangleq",!0),i(o,u,d,"∼","\\thicksim"),i(o,u,d,"≈","\\thickapprox"),i(o,u,d,"⫆","\\supseteqq",!0),i(o,u,d,"⋑","\\Supset",!0),i(o,u,d,"⊐","\\sqsupset",!0),i(o,u,d,"≽","\\succcurlyeq",!0),i(o,u,d,"⋟","\\curlyeqsucc",!0),i(o,u,d,"≿","\\succsim",!0),i(o,u,d,"⪸","\\succapprox",!0),i(o,u,d,"⊳","\\vartriangleright"),i(o,u,d,"⊵","\\trianglerighteq"),i(o,u,d,"⊩","\\Vdash",!0),i(o,u,d,"∣","\\shortmid"),i(o,u,d,"∥","\\shortparallel"),i(o,u,d,"≬","\\between",!0),i(o,u,d,"⋔","\\pitchfork",!0),i(o,u,d,"∝","\\varpropto"),i(o,u,d,"◀","\\blacktriangleleft"),i(o,u,d,"∴","\\therefore",!0),i(o,u,d,"∍","\\backepsilon"),i(o,u,d,"▶","\\blacktriangleright"),i(o,u,d,"∵","\\because",!0),i(o,u,d,"⋘","\\llless"),i(o,u,d,"⋙","\\gggtr"),i(o,u,C,"⊲","\\lhd"),i(o,u,C,"⊳","\\rhd"),i(o,u,d,"≂","\\eqsim",!0),i(o,l,d,"⋈","\\Join"),i(o,u,d,"≑","\\Doteq",!0),i(o,u,C,"∔","\\dotplus",!0),i(o,u,C,"∖","\\smallsetminus"),i(o,u,C,"⋒","\\Cap",!0),i(o,u,C,"⋓","\\Cup",!0),i(o,u,C,"⩞","\\doublebarwedge",!0),i(o,u,C,"⊟","\\boxminus",!0),i(o,u,C,"⊞","\\boxplus",!0),i(o,u,C,"⋇","\\divideontimes",!0),i(o,u,C,"⋉","\\ltimes",!0),i(o,u,C,"⋊","\\rtimes",!0),i(o,u,C,"⋋","\\leftthreetimes",!0),i(o,u,C,"⋌","\\rightthreetimes",!0),i(o,u,C,"⋏","\\curlywedge",!0),i(o,u,C,"⋎","\\curlyvee",!0),i(o,u,C,"⊝","\\circleddash",!0),i(o,u,C,"⊛","\\circledast",!0),i(o,u,C,"⋅","\\centerdot"),i(o,u,C,"⊺","\\intercal",!0),i(o,u,C,"⋒","\\doublecap"),i(o,u,C,"⋓","\\doublecup"),i(o,u,C,"⊠","\\boxtimes",!0),i(o,u,d,"⇢","\\dashrightarrow",!0),i(o,u,d,"⇠","\\dashleftarrow",!0),i(o,u,d,"⇇","\\leftleftarrows",!0),i(o,u,d,"⇆","\\leftrightarrows",!0),i(o,u,d,"⇚","\\Lleftarrow",!0),i(o,u,d,"↞","\\twoheadleftarrow",!0),i(o,u,d,"↢","\\leftarrowtail",!0),i(o,u,d,"↫","\\looparrowleft",!0),i(o,u,d,"⇋","\\leftrightharpoons",!0),i(o,u,d,"↶","\\curvearrowleft",!0),i(o,u,d,"↺","\\circlearrowleft",!0),i(o,u,d,"↰","\\Lsh",!0),i(o,u,d,"⇈","\\upuparrows",!0),i(o,u,d,"↿","\\upharpoonleft",!0),i(o,u,d,"⇃","\\downharpoonleft",!0),i(o,l,d,"⊶","\\origof",!0),i(o,l,d,"⊷","\\imageof",!0),i(o,u,d,"⊸","\\multimap",!0),i(o,u,d,"↭","\\leftrightsquigarrow",!0),i(o,u,d,"⇉","\\rightrightarrows",!0),i(o,u,d,"⇄","\\rightleftarrows",!0),i(o,u,d,"↠","\\twoheadrightarrow",!0),i(o,u,d,"↣","\\rightarrowtail",!0),i(o,u,d,"↬","\\looparrowright",!0),i(o,u,d,"↷","\\curvearrowright",!0),i(o,u,d,"↻","\\circlearrowright",!0),i(o,u,d,"↱","\\Rsh",!0),i(o,u,d,"⇊","\\downdownarrows",!0),i(o,u,d,"↾","\\upharpoonright",!0),i(o,u,d,"⇂","\\downharpoonright",!0),i(o,u,d,"⇝","\\rightsquigarrow",!0),i(o,u,d,"⇝","\\leadsto"),i(o,u,d,"⇛","\\Rrightarrow",!0),i(o,u,d,"↾","\\restriction"),i(o,l,f,"‘","`"),i(o,l,f,"$","\\$"),i(k,l,f,"$","\\$"),i(k,l,f,"$","\\textdollar"),i(o,l,f,"%","\\%"),i(k,l,f,"%","\\%"),i(o,l,f,"_","\\_"),i(k,l,f,"_","\\_"),i(k,l,f,"_","\\textunderscore"),i(o,l,f,"∠","\\angle",!0),i(o,l,f,"∞","\\infty",!0),i(o,l,f,"′","\\prime"),i(o,l,f,"△","\\triangle"),i(o,l,f,"Γ","\\Gamma",!0),i(o,l,f,"Δ","\\Delta",!0),i(o,l,f,"Θ","\\Theta",!0),i(o,l,f,"Λ","\\Lambda",!0),i(o,l,f,"Ξ","\\Xi",!0),i(o,l,f,"Π","\\Pi",!0),i(o,l,f,"Σ","\\Sigma",!0),i(o,l,f,"Υ","\\Upsilon",!0),i(o,l,f,"Φ","\\Phi",!0),i(o,l,f,"Ψ","\\Psi",!0),i(o,l,f,"Ω","\\Omega",!0),i(o,l,f,"A","Α"),i(o,l,f,"B","Β"),i(o,l,f,"E","Ε"),i(o,l,f,"Z","Ζ"),i(o,l,f,"H","Η"),i(o,l,f,"I","Ι"),i(o,l,f,"K","Κ"),i(o,l,f,"M","Μ"),i(o,l,f,"N","Ν"),i(o,l,f,"O","Ο"),i(o,l,f,"P","Ρ"),i(o,l,f,"T","Τ"),i(o,l,f,"X","Χ"),i(o,l,f,"¬","\\neg",!0),i(o,l,f,"¬","\\lnot"),i(o,l,f,"⊤","\\top"),i(o,l,f,"⊥","\\bot"),i(o,l,f,"∅","\\emptyset"),i(o,u,f,"∅","\\varnothing"),i(o,l,H,"α","\\alpha",!0),i(o,l,H,"β","\\beta",!0),i(o,l,H,"γ","\\gamma",!0),i(o,l,H,"δ","\\delta",!0),i(o,l,H,"ϵ","\\epsilon",!0),i(o,l,H,"ζ","\\zeta",!0),i(o,l,H,"η","\\eta",!0),i(o,l,H,"θ","\\theta",!0),i(o,l,H,"ι","\\iota",!0),i(o,l,H,"κ","\\kappa",!0),i(o,l,H,"λ","\\lambda",!0),i(o,l,H,"μ","\\mu",!0),i(o,l,H,"ν","\\nu",!0),i(o,l,H,"ξ","\\xi",!0),i(o,l,H,"ο","\\omicron",!0),i(o,l,H,"π","\\pi",!0),i(o,l,H,"ρ","\\rho",!0),i(o,l,H,"σ","\\sigma",!0),i(o,l,H,"τ","\\tau",!0),i(o,l,H,"υ","\\upsilon",!0),i(o,l,H,"ϕ","\\phi",!0),i(o,l,H,"χ","\\chi",!0),i(o,l,H,"ψ","\\psi",!0),i(o,l,H,"ω","\\omega",!0),i(o,l,H,"ε","\\varepsilon",!0),i(o,l,H,"ϑ","\\vartheta",!0),i(o,l,H,"ϖ","\\varpi",!0),i(o,l,H,"ϱ","\\varrho",!0),i(o,l,H,"ς","\\varsigma",!0),i(o,l,H,"φ","\\varphi",!0),i(o,l,C,"∗","*",!0),i(o,l,C,"+","+"),i(o,l,C,"−","-",!0),i(o,l,C,"⋅","\\cdot",!0),i(o,l,C,"∘","\\circ",!0),i(o,l,C,"÷","\\div",!0),i(o,l,C,"±","\\pm",!0),i(o,l,C,"×","\\times",!0),i(o,l,C,"∩","\\cap",!0),i(o,l,C,"∪","\\cup",!0),i(o,l,C,"∖","\\setminus",!0),i(o,l,C,"∧","\\land"),i(o,l,C,"∨","\\lor"),i(o,l,C,"∧","\\wedge",!0),i(o,l,C,"∨","\\vee",!0),i(o,l,f,"√","\\surd"),i(o,l,c0,"⟨","\\langle",!0),i(o,l,c0,"∣","\\lvert"),i(o,l,c0,"∥","\\lVert"),i(o,l,s0,"?","?"),i(o,l,s0,"!","!"),i(o,l,s0,"⟩","\\rangle",!0),i(o,l,s0,"∣","\\rvert"),i(o,l,s0,"∥","\\rVert"),i(o,l,d,"=","="),i(o,l,d,":",":"),i(o,l,d,"≈","\\approx",!0),i(o,l,d,"≅","\\cong",!0),i(o,l,d,"≥","\\ge"),i(o,l,d,"≥","\\geq",!0),i(o,l,d,"←","\\gets"),i(o,l,d,">","\\gt",!0),i(o,l,d,"∈","\\in",!0),i(o,l,d,"","\\@not"),i(o,l,d,"⊂","\\subset",!0),i(o,l,d,"⊃","\\supset",!0),i(o,l,d,"⊆","\\subseteq",!0),i(o,l,d,"⊇","\\supseteq",!0),i(o,u,d,"⊈","\\nsubseteq",!0),i(o,u,d,"⊉","\\nsupseteq",!0),i(o,l,d,"⊨","\\models"),i(o,l,d,"←","\\leftarrow",!0),i(o,l,d,"≤","\\le"),i(o,l,d,"≤","\\leq",!0),i(o,l,d,"<","\\lt",!0),i(o,l,d,"→","\\rightarrow",!0),i(o,l,d,"→","\\to"),i(o,u,d,"≱","\\ngeq",!0),i(o,u,d,"≰","\\nleq",!0),i(o,l,C0," ","\\ "),i(o,l,C0," ","\\space"),i(o,l,C0," ","\\nobreakspace"),i(k,l,C0," ","\\ "),i(k,l,C0," "," "),i(k,l,C0," ","\\space"),i(k,l,C0," ","\\nobreakspace"),i(o,l,C0,null,"\\nobreak"),i(o,l,C0,null,"\\allowbreak"),i(o,l,me,",",","),i(o,l,me,";",";"),i(o,u,C,"⊼","\\barwedge",!0),i(o,u,C,"⊻","\\veebar",!0),i(o,l,C,"⊙","\\odot",!0),i(o,l,C,"⊕","\\oplus",!0),i(o,l,C,"⊗","\\otimes",!0),i(o,l,f,"∂","\\partial",!0),i(o,l,C,"⊘","\\oslash",!0),i(o,u,C,"⊚","\\circledcirc",!0),i(o,u,C,"⊡","\\boxdot",!0),i(o,l,C,"△","\\bigtriangleup"),i(o,l,C,"▽","\\bigtriangledown"),i(o,l,C,"†","\\dagger"),i(o,l,C,"⋄","\\diamond"),i(o,l,C,"⋆","\\star"),i(o,l,C,"◃","\\triangleleft"),i(o,l,C,"▹","\\triangleright"),i(o,l,c0,"{","\\{"),i(k,l,f,"{","\\{"),i(k,l,f,"{","\\textbraceleft"),i(o,l,s0,"}","\\}"),i(k,l,f,"}","\\}"),i(k,l,f,"}","\\textbraceright"),i(o,l,c0,"{","\\lbrace"),i(o,l,s0,"}","\\rbrace"),i(o,l,c0,"[","\\lbrack",!0),i(k,l,f,"[","\\lbrack",!0),i(o,l,s0,"]","\\rbrack",!0),i(k,l,f,"]","\\rbrack",!0),i(o,l,c0,"(","\\lparen",!0),i(o,l,s0,")","\\rparen",!0),i(k,l,f,"<","\\textless",!0),i(k,l,f,">","\\textgreater",!0),i(o,l,c0,"⌊","\\lfloor",!0),i(o,l,s0,"⌋","\\rfloor",!0),i(o,l,c0,"⌈","\\lceil",!0),i(o,l,s0,"⌉","\\rceil",!0),i(o,l,f,"\\","\\backslash"),i(o,l,f,"∣","|"),i(o,l,f,"∣","\\vert"),i(k,l,f,"|","\\textbar",!0),i(o,l,f,"∥","\\|"),i(o,l,f,"∥","\\Vert"),i(k,l,f,"∥","\\textbardbl"),i(k,l,f,"~","\\textasciitilde"),i(k,l,f,"\\","\\textbackslash"),i(k,l,f,"^","\\textasciicircum"),i(o,l,d,"↑","\\uparrow",!0),i(o,l,d,"⇑","\\Uparrow",!0),i(o,l,d,"↓","\\downarrow",!0),i(o,l,d,"⇓","\\Downarrow",!0),i(o,l,d,"↕","\\updownarrow",!0),i(o,l,d,"⇕","\\Updownarrow",!0),i(o,l,r0,"∐","\\coprod"),i(o,l,r0,"⋁","\\bigvee"),i(o,l,r0,"⋀","\\bigwedge"),i(o,l,r0,"⨄","\\biguplus"),i(o,l,r0,"⋂","\\bigcap"),i(o,l,r0,"⋃","\\bigcup"),i(o,l,r0,"∫","\\int"),i(o,l,r0,"∫","\\intop"),i(o,l,r0,"∬","\\iint"),i(o,l,r0,"∭","\\iiint"),i(o,l,r0,"∏","\\prod"),i(o,l,r0,"∑","\\sum"),i(o,l,r0,"⨂","\\bigotimes"),i(o,l,r0,"⨁","\\bigoplus"),i(o,l,r0,"⨀","\\bigodot"),i(o,l,r0,"∮","\\oint"),i(o,l,r0,"∯","\\oiint"),i(o,l,r0,"∰","\\oiiint"),i(o,l,r0,"⨆","\\bigsqcup"),i(o,l,r0,"∫","\\smallint"),i(k,l,Z0,"…","\\textellipsis"),i(o,l,Z0,"…","\\mathellipsis"),i(k,l,Z0,"…","\\ldots",!0),i(o,l,Z0,"…","\\ldots",!0),i(o,l,Z0,"⋯","\\@cdots",!0),i(o,l,Z0,"⋱","\\ddots",!0),i(o,l,f,"⋮","\\varvdots"),i(k,l,f,"⋮","\\varvdots"),i(o,l,Z,"ˊ","\\acute"),i(o,l,Z,"ˋ","\\grave"),i(o,l,Z,"¨","\\ddot"),i(o,l,Z,"~","\\tilde"),i(o,l,Z,"ˉ","\\bar"),i(o,l,Z,"˘","\\breve"),i(o,l,Z,"ˇ","\\check"),i(o,l,Z,"^","\\hat"),i(o,l,Z,"⃗","\\vec"),i(o,l,Z,"˙","\\dot"),i(o,l,Z,"˚","\\mathring"),i(o,l,H,"","\\@imath"),i(o,l,H,"","\\@jmath"),i(o,l,f,"ı","ı"),i(o,l,f,"ȷ","ȷ"),i(k,l,f,"ı","\\i",!0),i(k,l,f,"ȷ","\\j",!0),i(k,l,f,"ß","\\ss",!0),i(k,l,f,"æ","\\ae",!0),i(k,l,f,"œ","\\oe",!0),i(k,l,f,"ø","\\o",!0),i(k,l,f,"Æ","\\AE",!0),i(k,l,f,"Œ","\\OE",!0),i(k,l,f,"Ø","\\O",!0),i(k,l,Z,"ˊ","\\'"),i(k,l,Z,"ˋ","\\`"),i(k,l,Z,"ˆ","\\^"),i(k,l,Z,"˜","\\~"),i(k,l,Z,"ˉ","\\="),i(k,l,Z,"˘","\\u"),i(k,l,Z,"˙","\\."),i(k,l,Z,"¸","\\c"),i(k,l,Z,"˚","\\r"),i(k,l,Z,"ˇ","\\v"),i(k,l,Z,"¨",'\\"'),i(k,l,Z,"˝","\\H"),i(k,l,Z,"◯","\\textcircled");var kr={"--":!0,"---":!0,"``":!0,"''":!0};i(k,l,f,"–","--",!0),i(k,l,f,"–","\\textendash"),i(k,l,f,"—","---",!0),i(k,l,f,"—","\\textemdash"),i(k,l,f,"‘","`",!0),i(k,l,f,"‘","\\textquoteleft"),i(k,l,f,"’","'",!0),i(k,l,f,"’","\\textquoteright"),i(k,l,f,"“","``",!0),i(k,l,f,"“","\\textquotedblleft"),i(k,l,f,"”","''",!0),i(k,l,f,"”","\\textquotedblright"),i(o,l,f,"°","\\degree",!0),i(k,l,f,"°","\\degree"),i(k,l,f,"°","\\textdegree",!0),i(o,l,f,"£","\\pounds"),i(o,l,f,"£","\\mathsterling",!0),i(k,l,f,"£","\\pounds"),i(k,l,f,"£","\\textsterling",!0),i(o,u,f,"✠","\\maltese"),i(k,u,f,"✠","\\maltese");for(var b1='0123456789/@."',Ee=0;Ee<14;Ee++){var Ct=b1.charAt(Ee);i(o,l,f,Ct,Ct)}for(var y1='0123456789!@*()-=+";:?/.,',Le=0;Le<25;Le++){var qt=y1.charAt(Le);i(k,l,f,qt,qt)}for(var Sr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",De=0;De<52;De++){var ce=Sr.charAt(De);i(o,l,H,ce,ce),i(k,l,f,ce,ce)}i(o,u,f,"C","ℂ"),i(k,u,f,"C","ℂ"),i(o,u,f,"H","ℍ"),i(k,u,f,"H","ℍ"),i(o,u,f,"N","ℕ"),i(k,u,f,"N","ℕ"),i(o,u,f,"P","ℙ"),i(k,u,f,"P","ℙ"),i(o,u,f,"Q","ℚ"),i(k,u,f,"Q","ℚ"),i(o,u,f,"R","ℝ"),i(k,u,f,"R","ℝ"),i(o,u,f,"Z","ℤ"),i(k,u,f,"Z","ℤ"),i(o,l,H,"h","ℎ"),i(k,l,H,"h","ℎ");for(var Y="",l0=0;l0<52;l0++){var e0=Sr.charAt(l0);i(o,l,H,e0,Y=String.fromCharCode(55349,56320+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56372+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56424+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56580+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56684+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56736+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56788+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56840+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56944+l0)),i(k,l,f,e0,Y),l0<26&&(i(o,l,H,e0,Y=String.fromCharCode(55349,56632+l0)),i(k,l,f,e0,Y),i(o,l,H,e0,Y=String.fromCharCode(55349,56476+l0)),i(k,l,f,e0,Y))}i(o,l,H,"k",Y="𝕜"),i(k,l,f,"k",Y);for(var X0=0;X0<10;X0++){var V0=X0.toString();i(o,l,H,V0,Y=String.fromCharCode(55349,57294+X0)),i(k,l,f,V0,Y),i(o,l,H,V0,Y=String.fromCharCode(55349,57314+X0)),i(k,l,f,V0,Y),i(o,l,H,V0,Y=String.fromCharCode(55349,57324+X0)),i(k,l,f,V0,Y),i(o,l,H,V0,Y=String.fromCharCode(55349,57334+X0)),i(k,l,f,V0,Y)}for(var Mr="ÐÞþ",Ve=0;Ve<3;Ve++){var pe=Mr.charAt(Ve);i(o,l,H,pe,pe),i(k,l,f,pe,pe)}var ue=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Nt=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],ke=function(t,e,r){return $[r][t]&&$[r][t].replace&&(t=$[r][t].replace),{value:t,metrics:dt(t,e,r)}},b0=function(t,e,r,a,n){var s,h=ke(t,e,r),c=h.metrics;if(t=h.value,c){var p=c.italic;(r==="text"||a&&a.font==="mathit")&&(p=0),s=new f0(t,c.height,c.depth,p,c.skew,c.width,n)}else typeof console<"u"&&console.warn("No character metrics for '"+t+"' in style '"+e+"' and mode '"+r+"'"),s=new f0(t,0,0,0,0,0,n);if(a){s.maxFontSize=a.sizeMultiplier,a.style.isTight()&&s.classes.push("mtight");var g=a.getColor();g&&(s.style.color=g)}return s},x1=(t,e)=>{if(U0(t.classes)!==U0(e.classes)||t.skew!==e.skew||t.maxFontSize!==e.maxFontSize)return!1;if(t.classes.length===1){var r=t.classes[0];if(r==="mbin"||r==="mord")return!1}for(var a in t.style)if(t.style.hasOwnProperty(a)&&t.style[a]!==e.style[a])return!1;for(var n in e.style)if(e.style.hasOwnProperty(n)&&t.style[n]!==e.style[n])return!1;return!0},ft=function(t){for(var e=0,r=0,a=0,n=0;n<t.children.length;n++){var s=t.children[n];s.height>e&&(e=s.height),s.depth>r&&(r=s.depth),s.maxFontSize>a&&(a=s.maxFontSize)}t.height=e,t.depth=r,t.maxFontSize=a},h0=function(t,e,r,a){var n=new ae(t,e,r,a);return ft(n),n},Rt=(t,e,r,a)=>new ae(t,e,r,a),Ht=function(t){var e=new re(t);return ft(e),e},de=function(t,e,r){var a="";switch(t){case"amsrm":a="AMS";break;case"textrm":a="Main";break;case"textsf":a="SansSerif";break;case"texttt":a="Typewriter";break;default:a=t}return a+"-"+(e==="textbf"&&r==="textit"?"BoldItalic":e==="textbf"?"Bold":e==="textit"?"Italic":"Regular")},It={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Ot={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},y={fontMap:It,makeSymbol:b0,mathsym:function(t,e,r,a){return a===void 0&&(a=[]),r.font==="boldsymbol"&&ke(t,"Main-Bold",e).metrics?b0(t,"Main-Bold",e,r,a.concat(["mathbf"])):t==="\\"||$[e][t].font==="main"?b0(t,"Main-Regular",e,r,a):b0(t,"AMS-Regular",e,r,a.concat(["amsrm"]))},makeSpan:h0,makeSvgSpan:Rt,makeLineSpan:function(t,e,r){var a=h0([t],[],e);return a.height=Math.max(r||e.fontMetrics().defaultRuleThickness,e.minRuleThickness),a.style.borderBottomWidth=A(a.height),a.maxFontSize=1,a},makeAnchor:function(t,e,r,a){var n=new gt(t,e,r,a);return ft(n),n},makeFragment:Ht,wrapFragment:function(t,e){return t instanceof re?h0([],[t],e):t},makeVList:function(t,e){for(var{children:r,depth:a}=function(j){if(j.positionType==="individualShift"){for(var F=j.children,n0=[F[0]],K=-F[0].shift-F[0].elem.depth,O0=K,o0=1;o0<F.length;o0++){var z0=-F[o0].shift-O0-F[o0].elem.depth,E0=z0-(F[o0-1].elem.height+F[o0-1].elem.depth);O0+=z0,n0.push({type:"kern",size:E0}),n0.push(F[o0])}return{children:n0,depth:K}}var A0;if(j.positionType==="top"){for(var v0=j.positionData,T0=0;T0<j.children.length;T0++){var i0=j.children[T0];v0-=i0.type==="kern"?i0.size:i0.elem.height+i0.elem.depth}A0=v0}else if(j.positionType==="bottom")A0=-j.positionData;else{var B0=j.children[0];if(B0.type!=="elem")throw new Error('First child must have type "elem".');if(j.positionType==="shift")A0=-B0.elem.depth-j.positionData;else{if(j.positionType!=="firstBaseline")throw new Error("Invalid positionType "+j.positionType+".");A0=-B0.elem.depth}}return{children:j.children,depth:A0}}(t),n=0,s=0;s<r.length;s++){var h=r[s];if(h.type==="elem"){var c=h.elem;n=Math.max(n,c.maxFontSize,c.height)}}n+=2;var p=h0(["pstrut"],[]);p.style.height=A(n);for(var g=[],b=a,x=a,v=a,w=0;w<r.length;w++){var z=r[w];if(z.type==="kern")v+=z.size;else{var T=z.elem,q=z.wrapperClasses||[],N=z.wrapperStyle||{},O=h0(q,[p,T],void 0,N);O.style.top=A(-n-v-T.depth),z.marginLeft&&(O.style.marginLeft=z.marginLeft),z.marginRight&&(O.style.marginRight=z.marginRight),g.push(O),v+=T.height+T.depth}b=Math.min(b,v),x=Math.max(x,v)}var D,G=h0(["vlist"],g);if(G.style.height=A(x),b<0){var E=h0([],[]),P=h0(["vlist"],[E]);P.style.height=A(-b);var V=h0(["vlist-s"],[new f0("​")]);D=[h0(["vlist-r"],[G,V]),h0(["vlist-r"],[P])]}else D=[h0(["vlist-r"],[G])];var W=h0(["vlist-t"],D);return D.length===2&&W.classes.push("vlist-t2"),W.height=x,W.depth=-b,W},makeOrd:function(t,e,r){var a=t.mode,n=t.text,s=["mord"],h=a==="math"||a==="text"&&e.font,c=h?e.font:e.fontFamily,p="",g="";if(n.charCodeAt(0)===55349&&([p,g]=function(D,G){var E=1024*(D.charCodeAt(0)-55296)+(D.charCodeAt(1)-56320)+65536,P=G==="math"?0:1;if(119808<=E&&E<120484){var V=Math.floor((E-119808)/26);return[ue[V][2],ue[V][P]]}if(120782<=E&&E<=120831){var W=Math.floor((E-120782)/10);return[Nt[W][2],Nt[W][P]]}if(E===120485||E===120486)return[ue[0][2],ue[0][P]];if(120486<E&&E<120782)return["",""];throw new M("Unsupported character: "+D)}(n,a)),p.length>0)return b0(n,p,a,e,s.concat(g));if(c){var b,x;if(c==="boldsymbol"){var v=function(D,G,E,P,V){return V!=="textord"&&ke(D,"Math-BoldItalic",G).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}}(n,a,0,0,r);b=v.fontName,x=[v.fontClass]}else h?(b=It[c].fontName,x=[c]):(b=de(c,e.fontWeight,e.fontShape),x=[c,e.fontWeight,e.fontShape]);if(ke(n,b,a).metrics)return b0(n,b,a,e,s.concat(x));if(kr.hasOwnProperty(n)&&b.slice(0,10)==="Typewriter"){for(var w=[],z=0;z<n.length;z++)w.push(b0(n[z],b,a,e,s.concat(x)));return Ht(w)}}if(r==="mathord")return b0(n,"Math-Italic",a,e,s.concat(["mathnormal"]));if(r==="textord"){var T=$[a][n]&&$[a][n].font;if(T==="ams"){var q=de("amsrm",e.fontWeight,e.fontShape);return b0(n,q,a,e,s.concat("amsrm",e.fontWeight,e.fontShape))}if(T!=="main"&&T){var N=de(T,e.fontWeight,e.fontShape);return b0(n,N,a,e,s.concat(N,e.fontWeight,e.fontShape))}var O=de("textrm",e.fontWeight,e.fontShape);return b0(n,O,a,e,s.concat(e.fontWeight,e.fontShape))}throw new Error("unexpected type: "+r+" in makeOrd")},makeGlue:(t,e)=>{var r=h0(["mspace"],[],e),a=Q(t,e);return r.style.marginRight=A(a),r},staticSvg:function(t,e){var[r,a,n]=Ot[t],s=new Y0(r),h=new H0([s],{width:A(a),height:A(n),style:"width:"+A(a),viewBox:"0 0 "+1e3*a+" "+1e3*n,preserveAspectRatio:"xMinYMin"}),c=Rt(["overlay"],[h],e);return c.height=n,c.style.height=A(n),c.style.width=A(a),c},svgData:Ot,tryCombineChars:t=>{for(var e=0;e<t.length-1;e++){var r=t[e],a=t[e+1];r instanceof f0&&a instanceof f0&&x1(r,a)&&(r.text+=a.text,r.height=Math.max(r.height,a.height),r.depth=Math.max(r.depth,a.depth),r.italic=a.italic,t.splice(e+1,1),e--)}return t}},J={number:3,unit:"mu"},_0={number:4,unit:"mu"},q0={number:5,unit:"mu"},w1={mord:{mop:J,mbin:_0,mrel:q0,minner:J},mop:{mord:J,mop:J,mrel:q0,minner:J},mbin:{mord:_0,mop:_0,mopen:_0,minner:_0},mrel:{mord:q0,mop:q0,mopen:q0,minner:q0},mopen:{},mclose:{mop:J,mbin:_0,mrel:q0,minner:J},mpunct:{mord:J,mop:J,mrel:q0,mopen:J,mclose:J,mpunct:J,minner:J},minner:{mord:J,mop:J,mbin:_0,mrel:q0,mopen:J,mpunct:J,minner:J}},k1={mord:{mop:J},mop:{mord:J,mop:J},mbin:{},mrel:{},mopen:{},mclose:{mop:J},mpunct:{},minner:{mop:J}},zr={},ze={},Ae={};function B(t){for(var{type:e,names:r,props:a,handler:n,htmlBuilder:s,mathmlBuilder:h}=t,c={type:e,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:a.allowedInMath===void 0||a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:n},p=0;p<r.length;++p)zr[r[p]]=c;e&&(s&&(ze[e]=s),h&&(Ae[e]=h))}function W0(t){var{type:e,htmlBuilder:r,mathmlBuilder:a}=t;B({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:a})}var Te=function(t){return t.type==="ordgroup"&&t.body.length===1?t.body[0]:t},t0=function(t){return t.type==="ordgroup"?t.body:[t]},I0=y.makeSpan,S1=["leftmost","mbin","mopen","mrel","mop","mpunct"],M1=["rightmost","mrel","mclose","mpunct"],z1={display:I.DISPLAY,text:I.TEXT,script:I.SCRIPT,scriptscript:I.SCRIPTSCRIPT},A1={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},a0=function(t,e,r,a){a===void 0&&(a=[null,null]);for(var n=[],s=0;s<t.length;s++){var h=U(t[s],e);if(h instanceof re){var c=h.children;n.push(...c)}else n.push(h)}if(y.tryCombineChars(n),!r)return n;var p=e;if(t.length===1){var g=t[0];g.type==="sizing"?p=e.havingSize(g.size):g.type==="styling"&&(p=e.havingStyle(z1[g.style]))}var b=I0([a[0]||"leftmost"],[],e),x=I0([a[1]||"rightmost"],[],e),v=r==="root";return Et(n,(w,z)=>{var T=z.classes[0],q=w.classes[0];T==="mbin"&&R.contains(M1,q)?z.classes[0]="mord":q==="mbin"&&R.contains(S1,T)&&(w.classes[0]="mord")},{node:b},x,v),Et(n,(w,z)=>{var T=it(z),q=it(w),N=T&&q?w.hasClass("mtight")?k1[T][q]:w1[T][q]:null;if(N)return y.makeGlue(N,p)},{node:b},x,v),n},Et=function t(e,r,a,n,s){n&&e.push(n);for(var h=0;h<e.length;h++){var c=e[h],p=Ar(c);if(p)t(p.children,r,a,null,s);else{var g=!c.hasClass("mspace");if(g){var b=r(c,a.node);b&&(a.insertAfter?a.insertAfter(b):(e.unshift(b),h++))}g?a.node=c:s&&c.hasClass("newline")&&(a.node=I0(["leftmost"])),a.insertAfter=(x=>v=>{e.splice(x+1,0,v),h++})(h)}}n&&e.pop()},Ar=function(t){return t instanceof re||t instanceof gt||t instanceof ae&&t.hasClass("enclosing")?t:null},T1=function t(e,r){var a=Ar(e);if(a){var n=a.children;if(n.length){if(r==="right")return t(n[n.length-1],"right");if(r==="left")return t(n[0],"left")}}return e},it=function(t,e){return t?(e&&(t=T1(t,e)),A1[t.classes[0]]||null):null},ee=function(t,e){var r=["nulldelimiter"].concat(t.baseSizingClasses());return I0(e.concat(r))},U=function(t,e,r){if(!t)return I0();if(ze[t.type]){var a=ze[t.type](t,e);if(r&&e.size!==r.size){a=I0(e.sizingClasses(r),[a],e);var n=e.sizeMultiplier/r.sizeMultiplier;a.height*=n,a.depth*=n}return a}throw new M("Got group of unknown type: '"+t.type+"'")};function ge(t,e){var r=I0(["base"],t,e),a=I0(["strut"]);return a.style.height=A(r.height+r.depth),r.depth&&(a.style.verticalAlign=A(-r.depth)),r.children.unshift(a),r}function ot(t,e){var r=null;t.length===1&&t[0].type==="tag"&&(r=t[0].tag,t=t[0].body);var a,n=a0(t,e,"root");n.length===2&&n[1].hasClass("tag")&&(a=n.pop());for(var s,h=[],c=[],p=0;p<n.length;p++)if(c.push(n[p]),n[p].hasClass("mbin")||n[p].hasClass("mrel")||n[p].hasClass("allowbreak")){for(var g=!1;p<n.length-1&&n[p+1].hasClass("mspace")&&!n[p+1].hasClass("newline");)p++,c.push(n[p]),n[p].hasClass("nobreak")&&(g=!0);g||(h.push(ge(c,e)),c=[])}else n[p].hasClass("newline")&&(c.pop(),c.length>0&&(h.push(ge(c,e)),c=[]),h.push(n[p]));c.length>0&&h.push(ge(c,e)),r?((s=ge(a0(r,e,!0))).classes=["tag"],h.push(s)):a&&h.push(a);var b=I0(["katex-html"],h);if(b.setAttribute("aria-hidden","true"),s){var x=s.children[0];x.style.height=A(b.height+b.depth),b.depth&&(x.style.verticalAlign=A(-b.depth))}return b}function Tr(t){return new re(t)}class u0{constructor(e,r,a){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=r||[],this.classes=a||[]}setAttribute(e,r){this.attributes[e]=r}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&e.setAttribute(r,this.attributes[r]);this.classes.length>0&&(e.className=U0(this.classes));for(var a=0;a<this.children.length;a++)if(this.children[a]instanceof M0&&this.children[a+1]instanceof M0){for(var n=this.children[a].toText()+this.children[++a].toText();this.children[a+1]instanceof M0;)n+=this.children[++a].toText();e.appendChild(new M0(n).toNode())}else e.appendChild(this.children[a].toNode());return e}toMarkup(){var e="<"+this.type;for(var r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="',e+=R.escape(this.attributes[r]),e+='"');this.classes.length>0&&(e+=' class ="'+R.escape(U0(this.classes))+'"'),e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</"+this.type+">"}toText(){return this.children.map(e=>e.toText()).join("")}}class M0{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return R.escape(this.toText())}toText(){return this.text}}var S={MathNode:u0,TextNode:M0,SpaceNode:class{constructor(t){this.width=void 0,this.character=void 0,this.width=t,this.character=t>=.05555&&t<=.05556?" ":t>=.1666&&t<=.1667?" ":t>=.2222&&t<=.2223?" ":t>=.2777&&t<=.2778?"  ":t>=-.05556&&t<=-.05555?" ⁣":t>=-.1667&&t<=-.1666?" ⁣":t>=-.2223&&t<=-.2222?" ⁣":t>=-.2778&&t<=-.2777?" ⁣":null}toNode(){if(this.character)return document.createTextNode(this.character);var t=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return t.setAttribute("width",A(this.width)),t}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+A(this.width)+'"/>'}toText(){return this.character?this.character:" "}},newDocumentFragment:Tr},d0=function(t,e,r){return!$[e][t]||!$[e][t].replace||t.charCodeAt(0)===55349||kr.hasOwnProperty(t)&&r&&(r.fontFamily&&r.fontFamily.slice(4,6)==="tt"||r.font&&r.font.slice(4,6)==="tt")||(t=$[e][t].replace),new S.TextNode(t)},vt=function(t){return t.length===1?t[0]:new S.MathNode("mrow",t)},st=function(t,e){if(e.fontFamily==="texttt")return"monospace";if(e.fontFamily==="textsf")return e.fontShape==="textit"&&e.fontWeight==="textbf"?"sans-serif-bold-italic":e.fontShape==="textit"?"sans-serif-italic":e.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(e.fontShape==="textit"&&e.fontWeight==="textbf")return"bold-italic";if(e.fontShape==="textit")return"italic";if(e.fontWeight==="textbf")return"bold";var r=e.font;if(!r||r==="mathnormal")return null;var a=t.mode;if(r==="mathit")return"italic";if(r==="boldsymbol")return t.type==="textord"?"bold":"bold-italic";if(r==="mathbf")return"bold";if(r==="mathbb")return"double-struck";if(r==="mathsfit")return"sans-serif-italic";if(r==="mathfrak")return"fraktur";if(r==="mathscr"||r==="mathcal")return"script";if(r==="mathsf")return"sans-serif";if(r==="mathtt")return"monospace";var n=t.text;return R.contains(["\\imath","\\jmath"],n)?null:($[a][n]&&$[a][n].replace&&(n=$[a][n].replace),dt(n,y.fontMap[r].fontName,a)?y.fontMap[r].variant:null)};function Pe(t){if(!t)return!1;if(t.type==="mi"&&t.children.length===1){var e=t.children[0];return e instanceof M0&&e.text==="."}if(t.type==="mo"&&t.children.length===1&&t.getAttribute("separator")==="true"&&t.getAttribute("lspace")==="0em"&&t.getAttribute("rspace")==="0em"){var r=t.children[0];return r instanceof M0&&r.text===","}return!1}var m0=function(t,e,r){if(t.length===1){var a=_(t[0],e);return r&&a instanceof u0&&a.type==="mo"&&(a.setAttribute("lspace","0em"),a.setAttribute("rspace","0em")),[a]}for(var n,s=[],h=0;h<t.length;h++){var c=_(t[h],e);if(c instanceof u0&&n instanceof u0){if(c.type==="mtext"&&n.type==="mtext"&&c.getAttribute("mathvariant")===n.getAttribute("mathvariant")){n.children.push(...c.children);continue}if(c.type==="mn"&&n.type==="mn"){n.children.push(...c.children);continue}if(Pe(c)&&n.type==="mn"){n.children.push(...c.children);continue}if(c.type==="mn"&&Pe(n))c.children=[...n.children,...c.children],s.pop();else if((c.type==="msup"||c.type==="msub")&&c.children.length>=1&&(n.type==="mn"||Pe(n))){var p=c.children[0];p instanceof u0&&p.type==="mn"&&(p.children=[...n.children,...p.children],s.pop())}else if(n.type==="mi"&&n.children.length===1){var g=n.children[0];if(g instanceof M0&&g.text==="̸"&&(c.type==="mo"||c.type==="mi"||c.type==="mn")){var b=c.children[0];b instanceof M0&&b.text.length>0&&(b.text=b.text.slice(0,1)+"̸"+b.text.slice(1),s.pop())}}}s.push(c),n=c}return s},G0=function(t,e,r){return vt(m0(t,e,r))},_=function(t,e){if(!t)return new S.MathNode("mrow");if(Ae[t.type])return Ae[t.type](t,e);throw new M("Got group of unknown type: '"+t.type+"'")};function Lt(t,e,r,a,n){var s,h=m0(t,r);s=h.length===1&&h[0]instanceof u0&&R.contains(["mrow","mtable"],h[0].type)?h[0]:new S.MathNode("mrow",h);var c=new S.MathNode("annotation",[new S.TextNode(e)]);c.setAttribute("encoding","application/x-tex");var p=new S.MathNode("semantics",[s,c]),g=new S.MathNode("math",[p]);g.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&g.setAttribute("display","block");var b=n?"katex":"katex-mathml";return y.makeSpan([b],[g])}var Br=function(t){return new N0({style:t.displayMode?I.DISPLAY:I.TEXT,maxSize:t.maxSize,minRuleThickness:t.minRuleThickness})},Cr=function(t,e){if(e.displayMode){var r=["katex-display"];e.leqno&&r.push("leqno"),e.fleqn&&r.push("fleqn"),t=y.makeSpan(r,[t])}return t},B1={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},C1={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},q1=function(t,e,r,a,n){var s,h=t.height+t.depth+r+a;if(/fbox|color|angl/.test(e)){if(s=y.makeSpan(["stretchy",e],[],n),e==="fbox"){var c=n.color&&n.getColor();c&&(s.style.borderColor=c)}}else{var p=[];/^[bx]cancel$/.test(e)&&p.push(new nt({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(e)&&p.push(new nt({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var g=new H0(p,{width:"100%",height:A(h)});s=y.makeSvgSpan([],[g],n)}return s.height=h,s.style.height=A(h),s},Ce=function(t){var e=new S.MathNode("mo",[new S.TextNode(B1[t.replace(/^\\/,"")])]);return e.setAttribute("stretchy","true"),e},qe=function(t,e){var{span:r,minWidth:a,height:n}=function(){var s=4e5,h=t.label.slice(1);if(R.contains(["widehat","widecheck","widetilde","utilde"],h)){var c,p,g,b=(q=t.base).type==="ordgroup"?q.body.length:1;if(b>5)h==="widehat"||h==="widecheck"?(c=420,s=2364,g=.42,p=h+"4"):(c=312,s=2340,g=.34,p="tilde4");else{var x=[1,1,2,2,3,3][b];h==="widehat"||h==="widecheck"?(s=[0,1062,2364,2364,2364][x],c=[0,239,300,360,420][x],g=[0,.24,.3,.3,.36,.42][x],p=h+x):(s=[0,600,1033,2339,2340][x],c=[0,260,286,306,312][x],g=[0,.26,.286,.3,.306,.34][x],p="tilde"+x)}var v=new Y0(p),w=new H0([v],{width:"100%",height:A(g),viewBox:"0 0 "+s+" "+c,preserveAspectRatio:"none"});return{span:y.makeSvgSpan([],[w],e),minWidth:0,height:g}}var z,T,q,N=[],O=C1[h],[D,G,E]=O,P=E/1e3,V=D.length;if(V===1)z=["hide-tail"],T=[O[3]];else if(V===2)z=["halfarrow-left","halfarrow-right"],T=["xMinYMin","xMaxYMin"];else{if(V!==3)throw new Error(`Correct katexImagesData or update code here to support
                    `+V+" children.");z=["brace-left","brace-center","brace-right"],T=["xMinYMin","xMidYMin","xMaxYMin"]}for(var W=0;W<V;W++){var j=new Y0(D[W]),F=new H0([j],{width:"400em",height:A(P),viewBox:"0 0 "+s+" "+E,preserveAspectRatio:T[W]+" slice"}),n0=y.makeSvgSpan([z[W]],[F],e);if(V===1)return{span:n0,minWidth:G,height:P};n0.style.height=A(P),N.push(n0)}return{span:y.makeSpan(["stretchy"],N,e),minWidth:G,height:P}}();return r.height=n,r.style.height=A(n),a>0&&(r.style.minWidth=A(a)),r};function L(t,e){if(!t||t.type!==e)throw new Error("Expected node of type "+e+", but got "+(t?"node of type "+t.type:String(t)));return t}function Fe(t){var e=Be(t);if(!e)throw new Error("Expected node of symbol group type, but got "+(t?"node of type "+t.type:String(t)));return e}function Be(t){return t&&(t.type==="atom"||v1.hasOwnProperty(t.type))?t:null}var lt=(t,e)=>{var r,a,n;t&&t.type==="supsub"?(r=(a=L(t.base,"accent")).base,t.base=r,n=function(q){if(q instanceof ae)return q;throw new Error("Expected span<HtmlDomNode> but got "+String(q)+".")}(U(t,e)),t.base=a):r=(a=L(t,"accent")).base;var s=U(r,e.havingCrampedStyle()),h=0;if(a.isShifty&&R.isCharacterBox(r)){var c=R.getBaseElem(r);h=Bt(U(c,e.havingCrampedStyle())).skew}var p,g=a.label==="\\c",b=g?s.height+s.depth:Math.min(s.height,e.fontMetrics().xHeight);if(a.isStretchy)p=qe(a,e),p=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"elem",elem:p,wrapperClasses:["svg-align"],wrapperStyle:h>0?{width:"calc(100% - "+A(2*h)+")",marginLeft:A(2*h)}:void 0}]},e);else{var x,v;a.label==="\\vec"?(x=y.staticSvg("vec",e),v=y.svgData.vec[1]):((x=Bt(x=y.makeOrd({mode:a.mode,text:a.label},e,"textord"))).italic=0,v=x.width,g&&(b+=x.depth)),p=y.makeSpan(["accent-body"],[x]);var w=a.label==="\\textcircled";w&&(p.classes.push("accent-full"),b=s.height);var z=h;w||(z-=v/2),p.style.left=A(z),a.label==="\\textcircled"&&(p.style.top=".2em"),p=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:-b},{type:"elem",elem:p}]},e)}var T=y.makeSpan(["mord","accent"],[p],e);return n?(n.children[0]=T,n.height=Math.max(T.height,n.height),n.classes[0]="mord",n):T},Dt=(t,e)=>{var r=t.isStretchy?Ce(t.label):new S.MathNode("mo",[d0(t.label,t.mode)]),a=new S.MathNode("mover",[_(t.base,e),r]);return a.setAttribute("accent","true"),a},N1=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(t=>"\\"+t).join("|"));B({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(t,e)=>{var r=Te(e[0]),a=!N1.test(t.funcName),n=!a||t.funcName==="\\widehat"||t.funcName==="\\widetilde"||t.funcName==="\\widecheck";return{type:"accent",mode:t.parser.mode,label:t.funcName,isStretchy:a,isShifty:n,base:r}},htmlBuilder:lt,mathmlBuilder:Dt}),B({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(t,e)=>{var r=e[0],a=t.parser.mode;return a==="math"&&(t.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+t.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:t.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:lt,mathmlBuilder:Dt}),B({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=e[0];return{type:"accentUnder",mode:r.mode,label:a,base:n}},htmlBuilder:(t,e)=>{var r=U(t.base,e),a=qe(t,e),n=t.label==="\\utilde"?.12:0,s=y.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:r}]},e);return y.makeSpan(["mord","accentunder"],[s],e)},mathmlBuilder:(t,e)=>{var r=Ce(t.label),a=new S.MathNode("munder",[_(t.base,e),r]);return a.setAttribute("accentunder","true"),a}});var fe=t=>{var e=new S.MathNode("mpadded",t?[t]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};B({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,r){var{parser:a,funcName:n}=t;return{type:"xArrow",mode:a.mode,label:n,body:e[0],below:r[0]}},htmlBuilder(t,e){var r,a=e.style,n=e.havingStyle(a.sup()),s=y.wrapFragment(U(t.body,n,e),e),h=t.label.slice(0,2)==="\\x"?"x":"cd";s.classes.push(h+"-arrow-pad"),t.below&&(n=e.havingStyle(a.sub()),(r=y.wrapFragment(U(t.below,n,e),e)).classes.push(h+"-arrow-pad"));var c,p=qe(t,e),g=-e.fontMetrics().axisHeight+.5*p.height,b=-e.fontMetrics().axisHeight-.5*p.height-.111;if((s.depth>.25||t.label==="\\xleftequilibrium")&&(b-=s.depth),r){var x=-e.fontMetrics().axisHeight+r.height+.5*p.height+.111;c=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:b},{type:"elem",elem:p,shift:g},{type:"elem",elem:r,shift:x}]},e)}else c=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:b},{type:"elem",elem:p,shift:g}]},e);return c.children[0].children[0].children[1].classes.push("svg-align"),y.makeSpan(["mrel","x-arrow"],[c],e)},mathmlBuilder(t,e){var r,a=Ce(t.label);if(a.setAttribute("minsize",t.label.charAt(0)==="x"?"1.75em":"3.0em"),t.body){var n=fe(_(t.body,e));if(t.below){var s=fe(_(t.below,e));r=new S.MathNode("munderover",[a,s,n])}else r=new S.MathNode("mover",[a,n])}else if(t.below){var h=fe(_(t.below,e));r=new S.MathNode("munder",[a,h])}else r=fe(),r=new S.MathNode("mover",[a,r]);return r}});var R1=y.makeSpan;function qr(t,e){var r=a0(t.body,e,!0);return R1([t.mclass],r,e)}function Nr(t,e){var r,a=m0(t.body,e);return t.mclass==="minner"?r=new S.MathNode("mpadded",a):t.mclass==="mord"?t.isCharacterBox?(r=a[0]).type="mi":r=new S.MathNode("mi",a):(t.isCharacterBox?(r=a[0]).type="mo":r=new S.MathNode("mo",a),t.mclass==="mbin"?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):t.mclass==="mpunct"?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):t.mclass==="mopen"||t.mclass==="mclose"?(r.attributes.lspace="0em",r.attributes.rspace="0em"):t.mclass==="minner"&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}B({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(t,e){var{parser:r,funcName:a}=t,n=e[0];return{type:"mclass",mode:r.mode,mclass:"m"+a.slice(5),body:t0(n),isCharacterBox:R.isCharacterBox(n)}},htmlBuilder:qr,mathmlBuilder:Nr});var Se=t=>{var e=t.type==="ordgroup"&&t.body.length?t.body[0]:t;return e.type!=="atom"||e.family!=="bin"&&e.family!=="rel"?"mord":"m"+e.family};B({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(t,e){var{parser:r}=t;return{type:"mclass",mode:r.mode,mclass:Se(e[0]),body:t0(e[1]),isCharacterBox:R.isCharacterBox(e[1])}}}),B({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(t,e){var r,{parser:a,funcName:n}=t,s=e[1],h=e[0];r=n!=="\\stackrel"?Se(s):"mrel";var c={type:"op",mode:s.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:n!=="\\stackrel",body:t0(s)},p={type:"supsub",mode:h.mode,base:c,sup:n==="\\underset"?null:h,sub:n==="\\underset"?h:null};return{type:"mclass",mode:a.mode,mclass:r,body:[p],isCharacterBox:R.isCharacterBox(p)}},htmlBuilder:qr,mathmlBuilder:Nr}),B({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(t,e){var{parser:r}=t;return{type:"pmb",mode:r.mode,mclass:Se(e[0]),body:t0(e[0])}},htmlBuilder(t,e){var r=a0(t.body,e,!0),a=y.makeSpan([t.mclass],r,e);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(t,e){var r=m0(t.body,e),a=new S.MathNode("mstyle",r);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var H1={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},Vt=t=>t.type==="textord"&&t.text==="@";function I1(t,e,r){var a=H1[t];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(a,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":var n={type:"atom",text:a,mode:"math",family:"rel"},s={type:"ordgroup",mode:"math",body:[r.callFunction("\\\\cdleft",[e[0]],[]),r.callFunction("\\Big",[n],[]),r.callFunction("\\\\cdright",[e[1]],[])]};return r.callFunction("\\\\cdparent",[s],[]);case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}B({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(t,e){var{parser:r,funcName:a}=t;return{type:"cdlabel",mode:r.mode,side:a.slice(4),label:e[0]}},htmlBuilder(t,e){var r=e.havingStyle(e.style.sup()),a=y.wrapFragment(U(t.label,r,e),e);return a.classes.push("cd-label-"+t.side),a.style.bottom=A(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(t,e){var r=new S.MathNode("mrow",[_(t.label,e)]);return(r=new S.MathNode("mpadded",[r])).setAttribute("width","0"),t.side==="left"&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new S.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),B({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(t,e){var{parser:r}=t;return{type:"cdlabelparent",mode:r.mode,fragment:e[0]}},htmlBuilder(t,e){var r=y.wrapFragment(U(t.fragment,e),e);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(t,e)=>new S.MathNode("mrow",[_(t.fragment,e)])}),B({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(t,e){for(var{parser:r}=t,a=L(e[0],"ordgroup").body,n="",s=0;s<a.length;s++)n+=L(a[s],"textord").text;var h,c=parseInt(n);if(isNaN(c))throw new M("\\@char has non-numeric argument "+n);if(c<0||c>=1114111)throw new M("\\@char with invalid code point "+n);return c<=65535?h=String.fromCharCode(c):(c-=65536,h=String.fromCharCode(55296+(c>>10),56320+(1023&c))),{type:"textord",mode:r.mode,text:h}}});var Pt=(t,e)=>{var r=a0(t.body,e.withColor(t.color),!1);return y.makeFragment(r)},Ft=(t,e)=>{var r=m0(t.body,e.withColor(t.color)),a=new S.MathNode("mstyle",r);return a.setAttribute("mathcolor",t.color),a};B({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(t,e){var{parser:r}=t,a=L(e[0],"color-token").color,n=e[1];return{type:"color",mode:r.mode,color:a,body:t0(n)}},htmlBuilder:Pt,mathmlBuilder:Ft}),B({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(t,e){var{parser:r,breakOnTokenText:a}=t,n=L(e[0],"color-token").color;r.gullet.macros.set("\\current@color",n);var s=r.parseExpression(!0,a);return{type:"color",mode:r.mode,color:n,body:s}},htmlBuilder:Pt,mathmlBuilder:Ft}),B({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(t,e,r){var{parser:a}=t,n=a.gullet.future().text==="["?a.parseSizeGroup(!0):null,s=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:s,size:n&&L(n,"size").value}},htmlBuilder(t,e){var r=y.makeSpan(["mspace"],[],e);return t.newLine&&(r.classes.push("newline"),t.size&&(r.style.marginTop=A(Q(t.size,e)))),r},mathmlBuilder(t,e){var r=new S.MathNode("mspace");return t.newLine&&(r.setAttribute("linebreak","newline"),t.size&&r.setAttribute("height",A(Q(t.size,e)))),r}});var Ge={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},Gt=t=>{var e=t.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new M("Expected a control sequence",t);return e},Ut=(t,e,r,a)=>{var n=t.gullet.macros.get(r.text);n==null&&(r.noexpand=!0,n={tokens:[r],numArgs:0,unexpandable:!t.gullet.isExpandable(r.text)}),t.gullet.macros.set(e,n,a)};B({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(t){var{parser:e,funcName:r}=t;e.consumeSpaces();var a=e.fetch();if(Ge[a.text])return r!=="\\global"&&r!=="\\\\globallong"||(a.text=Ge[a.text]),L(e.parseFunction(),"internal");throw new M("Invalid token after macro prefix",a)}}),B({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){var{parser:e,funcName:r}=t,a=e.gullet.popToken(),n=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(n))throw new M("Expected a control sequence",a);for(var s,h=0,c=[[]];e.gullet.future().text!=="{";)if((a=e.gullet.popToken()).text==="#"){if(e.gullet.future().text==="{"){s=e.gullet.future(),c[h].push("{");break}if(a=e.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new M('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==h+1)throw new M('Argument number "'+a.text+'" out of order');h++,c.push([])}else{if(a.text==="EOF")throw new M("Expected a macro definition");c[h].push(a.text)}var{tokens:p}=e.gullet.consumeArg();return s&&p.unshift(s),r!=="\\edef"&&r!=="\\xdef"||(p=e.gullet.expandTokens(p)).reverse(),e.gullet.macros.set(n,{tokens:p,numArgs:h,delimiters:c},r===Ge[r]),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){var{parser:e,funcName:r}=t,a=Gt(e.gullet.popToken());e.gullet.consumeSpaces();var n=(s=>{var h=s.gullet.popToken();return h.text==="="&&(h=s.gullet.popToken()).text===" "&&(h=s.gullet.popToken()),h})(e);return Ut(e,a,n,r==="\\\\globallet"),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){var{parser:e,funcName:r}=t,a=Gt(e.gullet.popToken()),n=e.gullet.popToken(),s=e.gullet.popToken();return Ut(e,a,s,r==="\\\\globalfuture"),e.gullet.pushToken(s),e.gullet.pushToken(n),{type:"internal",mode:e.mode}}});var Q0=function(t,e,r){var a=dt($.math[t]&&$.math[t].replace||t,e,r);if(!a)throw new Error("Unsupported symbol "+t+" and font size "+e+".");return a},bt=function(t,e,r,a){var n=r.havingBaseStyle(e),s=y.makeSpan(a.concat(n.sizingClasses(r)),[t],r),h=n.sizeMultiplier/r.sizeMultiplier;return s.height*=h,s.depth*=h,s.maxFontSize=n.sizeMultiplier,s},Rr=function(t,e,r){var a=e.havingBaseStyle(r),n=(1-e.sizeMultiplier/a.sizeMultiplier)*e.fontMetrics().axisHeight;t.classes.push("delimcenter"),t.style.top=A(n),t.height-=n,t.depth+=n},Hr=function(t,e,r,a,n,s){var h=function(p,g,b,x){return y.makeSymbol(p,"Size"+g+"-Regular",b,x)}(t,e,n,a),c=bt(y.makeSpan(["delimsizing","size"+e],[h],a),I.TEXT,a,s);return r&&Rr(c,a,I.TEXT),c},Ue=function(t,e,r){var a;return a=e==="Size1-Regular"?"delim-size1":"delim-size4",{type:"elem",elem:y.makeSpan(["delimsizinginner",a],[y.makeSpan([],[y.makeSymbol(t,e,r)])])}},Ye=function(t,e,r){var a=S0["Size4-Regular"][t.charCodeAt(0)]?S0["Size4-Regular"][t.charCodeAt(0)][4]:S0["Size1-Regular"][t.charCodeAt(0)][4],n=new Y0("inner",function(c,p){switch(c){case"⎜":return"M291 0 H417 V"+p+" H291z M291 0 H417 V"+p+" H291z";case"∣":return"M145 0 H188 V"+p+" H145z M145 0 H188 V"+p+" H145z";case"∥":return"M145 0 H188 V"+p+" H145z M145 0 H188 V"+p+" H145zM367 0 H410 V"+p+" H367z M367 0 H410 V"+p+" H367z";case"⎟":return"M457 0 H583 V"+p+" H457z M457 0 H583 V"+p+" H457z";case"⎢":return"M319 0 H403 V"+p+" H319z M319 0 H403 V"+p+" H319z";case"⎥":return"M263 0 H347 V"+p+" H263z M263 0 H347 V"+p+" H263z";case"⎪":return"M384 0 H504 V"+p+" H384z M384 0 H504 V"+p+" H384z";case"⏐":return"M312 0 H355 V"+p+" H312z M312 0 H355 V"+p+" H312z";case"‖":return"M257 0 H300 V"+p+" H257z M257 0 H300 V"+p+" H257zM478 0 H521 V"+p+" H478z M478 0 H521 V"+p+" H478z";default:return""}}(t,Math.round(1e3*e))),s=new H0([n],{width:A(a),height:A(e),style:"width:"+A(a),viewBox:"0 0 "+1e3*a+" "+Math.round(1e3*e),preserveAspectRatio:"xMinYMin"}),h=y.makeSvgSpan([],[s],r);return h.height=e,h.style.height=A(e),h.style.width=A(a),{type:"elem",elem:h}},ve={type:"kern",size:-.008},O1=["|","\\lvert","\\rvert","\\vert"],E1=["\\|","\\lVert","\\rVert","\\Vert"],Ir=function(t,e,r,a,n,s){var h,c,p,g,b="",x=0;h=p=g=t,c=null;var v="Size1-Regular";t==="\\uparrow"?p=g="⏐":t==="\\Uparrow"?p=g="‖":t==="\\downarrow"?h=p="⏐":t==="\\Downarrow"?h=p="‖":t==="\\updownarrow"?(h="\\uparrow",p="⏐",g="\\downarrow"):t==="\\Updownarrow"?(h="\\Uparrow",p="‖",g="\\Downarrow"):R.contains(O1,t)?(p="∣",b="vert",x=333):R.contains(E1,t)?(p="∥",b="doublevert",x=556):t==="["||t==="\\lbrack"?(h="⎡",p="⎢",g="⎣",v="Size4-Regular",b="lbrack",x=667):t==="]"||t==="\\rbrack"?(h="⎤",p="⎥",g="⎦",v="Size4-Regular",b="rbrack",x=667):t==="\\lfloor"||t==="⌊"?(p=h="⎢",g="⎣",v="Size4-Regular",b="lfloor",x=667):t==="\\lceil"||t==="⌈"?(h="⎡",p=g="⎢",v="Size4-Regular",b="lceil",x=667):t==="\\rfloor"||t==="⌋"?(p=h="⎥",g="⎦",v="Size4-Regular",b="rfloor",x=667):t==="\\rceil"||t==="⌉"?(h="⎤",p=g="⎥",v="Size4-Regular",b="rceil",x=667):t==="("||t==="\\lparen"?(h="⎛",p="⎜",g="⎝",v="Size4-Regular",b="lparen",x=875):t===")"||t==="\\rparen"?(h="⎞",p="⎟",g="⎠",v="Size4-Regular",b="rparen",x=875):t==="\\{"||t==="\\lbrace"?(h="⎧",c="⎨",g="⎩",p="⎪",v="Size4-Regular"):t==="\\}"||t==="\\rbrace"?(h="⎫",c="⎬",g="⎭",p="⎪",v="Size4-Regular"):t==="\\lgroup"||t==="⟮"?(h="⎧",g="⎩",p="⎪",v="Size4-Regular"):t==="\\rgroup"||t==="⟯"?(h="⎫",g="⎭",p="⎪",v="Size4-Regular"):t==="\\lmoustache"||t==="⎰"?(h="⎧",g="⎭",p="⎪",v="Size4-Regular"):t!=="\\rmoustache"&&t!=="⎱"||(h="⎫",g="⎩",p="⎪",v="Size4-Regular");var w=Q0(h,v,n),z=w.height+w.depth,T=Q0(p,v,n),q=T.height+T.depth,N=Q0(g,v,n),O=N.height+N.depth,D=0,G=1;if(c!==null){var E=Q0(c,v,n);D=E.height+E.depth,G=2}var P=z+O+D,V=P+Math.max(0,Math.ceil((e-P)/(G*q)))*G*q,W=a.fontMetrics().axisHeight;r&&(W*=a.sizeMultiplier);var j=V/2-W,F=[];if(b.length>0){var n0=V-z-O,K=Math.round(1e3*V),O0=function(L0,X){switch(L0){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+X+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+X+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+X+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+X+" v1759 h84z";case"vert":return"M145 15 v585 v"+X+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-X+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+X+" v585 h43z";case"doublevert":return"M145 15 v585 v"+X+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-X+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+X+` v585 h43z
M367 15 v585 v`+X+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-X+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+X+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+X+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+X+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+X+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+X+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+X+` v602 h84z
M403 1759 V0 H319 V1759 v`+X+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+X+` v602 h84z
M347 1759 V0 h-84 V1759 v`+X+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(X+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(X+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(X+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(X+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}}(b,Math.round(1e3*n0)),o0=new Y0(b,O0),z0=(x/1e3).toFixed(3)+"em",E0=(K/1e3).toFixed(3)+"em",A0=new H0([o0],{width:z0,height:E0,viewBox:"0 0 "+x+" "+K}),v0=y.makeSvgSpan([],[A0],a);v0.height=K/1e3,v0.style.width=z0,v0.style.height=E0,F.push({type:"elem",elem:v0})}else{if(F.push(Ue(g,v,n)),F.push(ve),c===null){var T0=V-z-O+.016;F.push(Ye(p,T0,a))}else{var i0=(V-z-O-D)/2+.016;F.push(Ye(p,i0,a)),F.push(ve),F.push(Ue(c,v,n)),F.push(ve),F.push(Ye(p,i0,a))}F.push(ve),F.push(Ue(h,v,n))}var B0=a.havingBaseStyle(I.TEXT),Re=y.makeVList({positionType:"bottom",positionData:j,children:F},B0);return bt(y.makeSpan(["delimsizing","mult"],[Re],B0),I.TEXT,a,s)},Xe=.08,_e=function(t,e,r,a,n){var s=function(p,g,b){g*=1e3;var x="";switch(p){case"sqrtMain":x=function(v,w){return"M95,"+(622+v+w)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+v/2.075+" -"+v+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+v)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+v)+" "+w+"h400000v"+(40+v)+"h-400000z"}(g,$0);break;case"sqrtSize1":x=function(v,w){return"M263,"+(601+v+w)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+v/2.084+" -"+v+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+v)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+v)+" "+w+"h400000v"+(40+v)+"h-400000z"}(g,$0);break;case"sqrtSize2":x=function(v,w){return"M983 "+(10+v+w)+`
l`+v/3.13+" -"+v+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+v)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+v)+" "+w+"h400000v"+(40+v)+"h-400000z"}(g,$0);break;case"sqrtSize3":x=function(v,w){return"M424,"+(2398+v+w)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+v/4.223+" -"+v+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+v)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+v)+" "+w+`
h400000v`+(40+v)+"h-400000z"}(g,$0);break;case"sqrtSize4":x=function(v,w){return"M473,"+(2713+v+w)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+v/5.298+" -"+v+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+v)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+v)+" "+w+"h400000v"+(40+v)+"H1017.7z"}(g,$0);break;case"sqrtTall":x=function(v,w,z){return"M702 "+(v+w)+"H400000"+(40+v)+`
H742v`+(z-54-w-v)+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+w+"H400000v"+(40+v)+"H742z"}(g,$0,b)}return x}(t,a,r),h=new Y0(t,s),c=new H0([h],{width:"400em",height:A(e),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return y.makeSvgSpan(["hide-tail"],[c],n)},Or=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],L1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],Er=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],J0=[0,1.2,1.8,2.4,3],D1=[{type:"small",style:I.SCRIPTSCRIPT},{type:"small",style:I.SCRIPT},{type:"small",style:I.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],V1=[{type:"small",style:I.SCRIPTSCRIPT},{type:"small",style:I.SCRIPT},{type:"small",style:I.TEXT},{type:"stack"}],Lr=[{type:"small",style:I.SCRIPTSCRIPT},{type:"small",style:I.SCRIPT},{type:"small",style:I.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],P1=function(t){if(t.type==="small")return"Main-Regular";if(t.type==="large")return"Size"+t.size+"-Regular";if(t.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+t.type+"' here.")},Dr=function(t,e,r,a){for(var n=Math.min(2,3-a.style.size);n<r.length&&r[n].type!=="stack";n++){var s=Q0(t,P1(r[n]),"math"),h=s.height+s.depth;if(r[n].type==="small"&&(h*=a.havingBaseStyle(r[n].style).sizeMultiplier),h>e)return r[n]}return r[r.length-1]},Yt=function(t,e,r,a,n,s){var h;t==="<"||t==="\\lt"||t==="⟨"?t="\\langle":t!==">"&&t!=="\\gt"&&t!=="⟩"||(t="\\rangle"),h=R.contains(Er,t)?D1:R.contains(Or,t)?Lr:V1;var c=Dr(t,e,h,a);return c.type==="small"?function(p,g,b,x,v,w){var z=y.makeSymbol(p,"Main-Regular",v,x),T=bt(z,g,x,w);return b&&Rr(T,x,g),T}(t,c.style,r,a,n,s):c.type==="large"?Hr(t,c.size,r,a,n,s):Ir(t,e,r,a,n,s)},R0={sqrtImage:function(t,e){var r,a,n=e.havingBaseSizing(),s=Dr("\\surd",t*n.sizeMultiplier,Lr,n),h=n.sizeMultiplier,c=Math.max(0,e.minRuleThickness-e.fontMetrics().sqrtRuleThickness),p=0,g=0,b=0;return s.type==="small"?(t<1?h=1:t<1.4&&(h=.7),g=(1+c)/h,(r=_e("sqrtMain",p=(1+c+Xe)/h,b=1e3+1e3*c+80,c,e)).style.minWidth="0.853em",a=.833/h):s.type==="large"?(b=1080*J0[s.size],g=(J0[s.size]+c)/h,p=(J0[s.size]+c+Xe)/h,(r=_e("sqrtSize"+s.size,p,b,c,e)).style.minWidth="1.02em",a=1/h):(p=t+c+Xe,g=t+c,b=Math.floor(1e3*t+c)+80,(r=_e("sqrtTall",p,b,c,e)).style.minWidth="0.742em",a=1.056),r.height=g,r.style.height=A(p),{span:r,advanceWidth:a,ruleWidth:(e.fontMetrics().sqrtRuleThickness+c)*h}},sizedDelim:function(t,e,r,a,n){if(t==="<"||t==="\\lt"||t==="⟨"?t="\\langle":t!==">"&&t!=="\\gt"&&t!=="⟩"||(t="\\rangle"),R.contains(Or,t)||R.contains(Er,t))return Hr(t,e,!1,r,a,n);if(R.contains(L1,t))return Ir(t,J0[e],!1,r,a,n);throw new M("Illegal delimiter: '"+t+"'")},sizeToMaxHeight:J0,customSizedDelim:Yt,leftRightDelim:function(t,e,r,a,n,s){var h=a.fontMetrics().axisHeight*a.sizeMultiplier,c=5/a.fontMetrics().ptPerEm,p=Math.max(e-h,r+h),g=Math.max(p/500*901,2*p-c);return Yt(t,g,!0,a,n,s)}},Xt={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},F1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function be(t,e){var r=Be(t);if(r&&R.contains(F1,r.text))return r;throw new M(r?"Invalid delimiter '"+r.text+"' after '"+e.funcName+"'":"Invalid delimiter type '"+t.type+"'",t)}function _t(t){if(!t.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}B({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(t,e)=>{var r=be(e[0],t);return{type:"delimsizing",mode:t.parser.mode,size:Xt[t.funcName].size,mclass:Xt[t.funcName].mclass,delim:r.text}},htmlBuilder:(t,e)=>t.delim==="."?y.makeSpan([t.mclass]):R0.sizedDelim(t.delim,t.size,e,t.mode,[t.mclass]),mathmlBuilder:t=>{var e=[];t.delim!=="."&&e.push(d0(t.delim,t.mode));var r=new S.MathNode("mo",e);t.mclass==="mopen"||t.mclass==="mclose"?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var a=A(R0.sizeToMaxHeight[t.size]);return r.setAttribute("minsize",a),r.setAttribute("maxsize",a),r}}),B({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{var r=t.parser.gullet.macros.get("\\current@color");if(r&&typeof r!="string")throw new M("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:t.parser.mode,delim:be(e[0],t).text,color:r}}}),B({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{var r=be(e[0],t),a=t.parser;++a.leftrightDepth;var n=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var s=L(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:n,left:r.text,right:s.delim,rightColor:s.color}},htmlBuilder:(t,e)=>{_t(t);for(var r,a,n=a0(t.body,e,!0,["mopen","mclose"]),s=0,h=0,c=!1,p=0;p<n.length;p++)n[p].isMiddle?c=!0:(s=Math.max(n[p].height,s),h=Math.max(n[p].depth,h));if(s*=e.sizeMultiplier,h*=e.sizeMultiplier,r=t.left==="."?ee(e,["mopen"]):R0.leftRightDelim(t.left,s,h,e,t.mode,["mopen"]),n.unshift(r),c)for(var g=1;g<n.length;g++){var b=n[g].isMiddle;b&&(n[g]=R0.leftRightDelim(b.delim,s,h,b.options,t.mode,[]))}if(t.right===".")a=ee(e,["mclose"]);else{var x=t.rightColor?e.withColor(t.rightColor):e;a=R0.leftRightDelim(t.right,s,h,x,t.mode,["mclose"])}return n.push(a),y.makeSpan(["minner"],n,e)},mathmlBuilder:(t,e)=>{_t(t);var r=m0(t.body,e);if(t.left!=="."){var a=new S.MathNode("mo",[d0(t.left,t.mode)]);a.setAttribute("fence","true"),r.unshift(a)}if(t.right!=="."){var n=new S.MathNode("mo",[d0(t.right,t.mode)]);n.setAttribute("fence","true"),t.rightColor&&n.setAttribute("mathcolor",t.rightColor),r.push(n)}return vt(r)}}),B({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{var r=be(e[0],t);if(!t.parser.leftrightDepth)throw new M("\\middle without preceding \\left",r);return{type:"middle",mode:t.parser.mode,delim:r.text}},htmlBuilder:(t,e)=>{var r;if(t.delim===".")r=ee(e,[]);else{r=R0.sizedDelim(t.delim,1,e,t.mode,[]);var a={delim:t.delim,options:e};r.isMiddle=a}return r},mathmlBuilder:(t,e)=>{var r=t.delim==="\\vert"||t.delim==="|"?d0("|","text"):d0(t.delim,t.mode),a=new S.MathNode("mo",[r]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var We=(t,e)=>{var r,a,n,s=y.wrapFragment(U(t.body,e),e),h=t.label.slice(1),c=e.sizeMultiplier,p=0,g=R.isCharacterBox(t.body);if(h==="sout")(r=y.makeSpan(["stretchy","sout"])).height=e.fontMetrics().defaultRuleThickness/c,p=-.5*e.fontMetrics().xHeight;else if(h==="phase"){var b=Q({number:.6,unit:"pt"},e),x=Q({number:.35,unit:"ex"},e);c/=e.havingBaseSizing().sizeMultiplier;var v=s.height+s.depth+b+x;s.style.paddingLeft=A(v/2+b);var w=Math.floor(1e3*v*c),z="M400000 "+(a=w)+" H0 L"+a/2+" 0 l65 45 L145 "+(a-80)+" H400000z",T=new H0([new Y0("phase",z)],{width:"400em",height:A(w/1e3),viewBox:"0 0 400000 "+w,preserveAspectRatio:"xMinYMin slice"});(r=y.makeSvgSpan(["hide-tail"],[T],e)).style.height=A(v),p=s.depth+b+x}else{/cancel/.test(h)?g||s.classes.push("cancel-pad"):h==="angl"?s.classes.push("anglpad"):s.classes.push("boxpad");var q=0,N=0,O=0;/box/.test(h)?(O=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),N=q=e.fontMetrics().fboxsep+(h==="colorbox"?0:O)):h==="angl"?(q=4*(O=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness)),N=Math.max(0,.25-s.depth)):N=q=g?.2:0,r=q1(s,h,q,N,e),/fbox|boxed|fcolorbox/.test(h)?(r.style.borderStyle="solid",r.style.borderWidth=A(O)):h==="angl"&&O!==.049&&(r.style.borderTopWidth=A(O),r.style.borderRightWidth=A(O)),p=s.depth+N,t.backgroundColor&&(r.style.backgroundColor=t.backgroundColor,t.borderColor&&(r.style.borderColor=t.borderColor))}if(t.backgroundColor)n=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:p},{type:"elem",elem:s,shift:0}]},e);else{var D=/cancel|phase/.test(h)?["svg-align"]:[];n=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:0},{type:"elem",elem:r,shift:p,wrapperClasses:D}]},e)}return/cancel/.test(h)&&(n.height=s.height,n.depth=s.depth),/cancel/.test(h)&&!g?y.makeSpan(["mord","cancel-lap"],[n],e):y.makeSpan(["mord"],[n],e)},je=(t,e)=>{var r=0,a=new S.MathNode(t.label.indexOf("colorbox")>-1?"mpadded":"menclose",[_(t.body,e)]);switch(t.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*r+"pt"),a.setAttribute("height","+"+2*r+"pt"),a.setAttribute("lspace",r+"pt"),a.setAttribute("voffset",r+"pt"),t.label==="\\fcolorbox"){var n=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);a.setAttribute("style","border: "+n+"em solid "+String(t.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return t.backgroundColor&&a.setAttribute("mathbackground",t.backgroundColor),a};B({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(t,e,r){var{parser:a,funcName:n}=t,s=L(e[0],"color-token").color,h=e[1];return{type:"enclose",mode:a.mode,label:n,backgroundColor:s,body:h}},htmlBuilder:We,mathmlBuilder:je}),B({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(t,e,r){var{parser:a,funcName:n}=t,s=L(e[0],"color-token").color,h=L(e[1],"color-token").color,c=e[2];return{type:"enclose",mode:a.mode,label:n,backgroundColor:h,borderColor:s,body:c}},htmlBuilder:We,mathmlBuilder:je}),B({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(t,e){var{parser:r}=t;return{type:"enclose",mode:r.mode,label:"\\fbox",body:e[0]}}}),B({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(t,e){var{parser:r,funcName:a}=t,n=e[0];return{type:"enclose",mode:r.mode,label:a,body:n}},htmlBuilder:We,mathmlBuilder:je}),B({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(t,e){var{parser:r}=t;return{type:"enclose",mode:r.mode,label:"\\angl",body:e[0]}}});var Vr={};function y0(t){for(var{type:e,names:r,props:a,handler:n,htmlBuilder:s,mathmlBuilder:h}=t,c={type:e,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},p=0;p<r.length;++p)Vr[r[p]]=c;s&&(ze[e]=s),h&&(Ae[e]=h)}var Pr={};function m(t,e){Pr[t]=e}function Wt(t){var e=[];t.consumeSpaces();var r=t.fetch().text;for(r==="\\relax"&&(t.consume(),t.consumeSpaces(),r=t.fetch().text);r==="\\hline"||r==="\\hdashline";)t.consume(),e.push(r==="\\hdashline"),t.consumeSpaces(),r=t.fetch().text;return e}var Me=t=>{if(!t.parser.settings.displayMode)throw new M("{"+t.envName+"} can be used only in display mode.")};function ht(t){if(t.indexOf("ed")===-1)return t.indexOf("*")===-1}function P0(t,e,r){var{hskipBeforeAndAfter:a,addJot:n,cols:s,arraystretch:h,colSeparationType:c,autoTag:p,singleRow:g,emptySingleRow:b,maxNumCols:x,leqno:v}=e;if(t.gullet.beginGroup(),g||t.gullet.macros.set("\\cr","\\\\\\relax"),!h){var w=t.gullet.expandMacroAsText("\\arraystretch");if(w==null)h=1;else if(!(h=parseFloat(w))||h<0)throw new M("Invalid \\arraystretch: "+w)}t.gullet.beginGroup();var z=[],T=[z],q=[],N=[],O=p!=null?[]:void 0;function D(){p&&t.gullet.macros.set("\\@eqnsw","1",!0)}function G(){O&&(t.gullet.macros.get("\\df@tag")?(O.push(t.subparse([new g0("\\df@tag")])),t.gullet.macros.set("\\df@tag",void 0,!0)):O.push(!!p&&t.gullet.macros.get("\\@eqnsw")==="1"))}for(D(),N.push(Wt(t));;){var E=t.parseExpression(!1,g?"\\end":"\\\\");t.gullet.endGroup(),t.gullet.beginGroup(),E={type:"ordgroup",mode:t.mode,body:E},r&&(E={type:"styling",mode:t.mode,style:r,body:[E]}),z.push(E);var P=t.fetch().text;if(P==="&"){if(x&&z.length===x){if(g||c)throw new M("Too many tab characters: &",t.nextToken);t.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}t.consume()}else{if(P==="\\end"){G(),z.length===1&&E.type==="styling"&&E.body[0].body.length===0&&(T.length>1||!b)&&T.pop(),N.length<T.length+1&&N.push([]);break}if(P!=="\\\\")throw new M("Expected & or \\\\ or \\cr or \\end",t.nextToken);t.consume();var V=void 0;t.gullet.future().text!==" "&&(V=t.parseSizeGroup(!0)),q.push(V?V.value:null),G(),N.push(Wt(t)),z=[],T.push(z),D()}}return t.gullet.endGroup(),t.gullet.endGroup(),{type:"array",mode:t.mode,addJot:n,arraystretch:h,body:T,cols:s,rowGaps:q,hskipBeforeAndAfter:a,hLinesBeforeRow:N,colSeparationType:c,tags:O,leqno:v}}function $e(t){return t.slice(0,1)==="d"?"display":"text"}var x0=function(t,e){var r,a,n=t.body.length,s=t.hLinesBeforeRow,h=0,c=new Array(n),p=[],g=Math.max(e.fontMetrics().arrayRuleWidth,e.minRuleThickness),b=1/e.fontMetrics().ptPerEm,x=5*b;t.colSeparationType&&t.colSeparationType==="small"&&(x=e.havingStyle(I.SCRIPT).sizeMultiplier/e.sizeMultiplier*.2778);var v=t.colSeparationType==="CD"?Q({number:3,unit:"ex"},e):12*b,w=3*b,z=t.arraystretch*v,T=.7*z,q=.3*z,N=0;function O(se){for(var le=0;le<se.length;++le)le>0&&(N+=.25),p.push({pos:N,isDashed:se[le]})}for(O(s[0]),r=0;r<t.body.length;++r){var D=t.body[r],G=T,E=q;h<D.length&&(h=D.length);var P=new Array(D.length);for(a=0;a<D.length;++a){var V=U(D[a],e);E<V.depth&&(E=V.depth),G<V.height&&(G=V.height),P[a]=V}var W=t.rowGaps[r],j=0;W&&(j=Q(W,e))>0&&(E<(j+=q)&&(E=j),j=0),t.addJot&&(E+=w),P.height=G,P.depth=E,N+=G,P.pos=N,N+=E+j,c[r]=P,O(s[r+1])}var F,n0,K=N/2+e.fontMetrics().axisHeight,O0=t.cols||[],o0=[],z0=[];if(t.tags&&t.tags.some(se=>se))for(r=0;r<n;++r){var E0=c[r],A0=E0.pos-K,v0=t.tags[r],T0=void 0;(T0=v0===!0?y.makeSpan(["eqn-num"],[],e):v0===!1?y.makeSpan([],[],e):y.makeSpan([],a0(v0,e,!0),e)).depth=E0.depth,T0.height=E0.height,z0.push({type:"elem",elem:T0,shift:A0})}for(a=0,n0=0;a<h||n0<O0.length;++a,++n0){for(var i0=O0[n0]||{},B0=!0;i0.type==="separator";){if(B0||((F=y.makeSpan(["arraycolsep"],[])).style.width=A(e.fontMetrics().doubleRuleSep),o0.push(F)),i0.separator!=="|"&&i0.separator!==":")throw new M("Invalid separator type: "+i0.separator);var Re=i0.separator==="|"?"solid":"dashed",L0=y.makeSpan(["vertical-separator"],[],e);L0.style.height=A(N),L0.style.borderRightWidth=A(g),L0.style.borderRightStyle=Re,L0.style.margin="0 "+A(-g/2);var X=N-K;X&&(L0.style.verticalAlign=A(-X)),o0.push(L0),i0=O0[++n0]||{},B0=!1}if(!(a>=h)){var ne=void 0;(a>0||t.hskipBeforeAndAfter)&&(ne=R.deflt(i0.pregap,x))!==0&&((F=y.makeSpan(["arraycolsep"],[])).style.width=A(ne),o0.push(F));var j0=[];for(r=0;r<n;++r){var ie=c[r],oe=ie[a];if(oe){var Kr=ie.pos-K;oe.depth=ie.depth,oe.height=ie.height,j0.push({type:"elem",elem:oe,shift:Kr})}}j0=y.makeVList({positionType:"individualShift",children:j0},e),j0=y.makeSpan(["col-align-"+(i0.align||"c")],[j0]),o0.push(j0),(a<h-1||t.hskipBeforeAndAfter)&&(ne=R.deflt(i0.postgap,x))!==0&&((F=y.makeSpan(["arraycolsep"],[])).style.width=A(ne),o0.push(F))}}if(c=y.makeSpan(["mtable"],o0),p.length>0){for(var Jr=y.makeLineSpan("hline",e,g),Qr=y.makeLineSpan("hdashline",e,g),He=[{type:"elem",elem:c,shift:0}];p.length>0;){var wt=p.pop(),kt=wt.pos-K;wt.isDashed?He.push({type:"elem",elem:Qr,shift:kt}):He.push({type:"elem",elem:Jr,shift:kt})}c=y.makeVList({positionType:"individualShift",children:He},e)}if(z0.length===0)return y.makeSpan(["mord"],[c],e);var Ie=y.makeVList({positionType:"individualShift",children:z0},e);return Ie=y.makeSpan(["tag"],[Ie],e),y.makeFragment([c,Ie])},G1={c:"center ",l:"left ",r:"right "},w0=function(t,e){for(var r=[],a=new S.MathNode("mtd",[],["mtr-glue"]),n=new S.MathNode("mtd",[],["mml-eqn-num"]),s=0;s<t.body.length;s++){for(var h=t.body[s],c=[],p=0;p<h.length;p++)c.push(new S.MathNode("mtd",[_(h[p],e)]));t.tags&&t.tags[s]&&(c.unshift(a),c.push(a),t.leqno?c.unshift(n):c.push(n)),r.push(new S.MathNode("mtr",c))}var g=new S.MathNode("mtable",r),b=t.arraystretch===.5?.1:.16+t.arraystretch-1+(t.addJot?.09:0);g.setAttribute("rowspacing",A(b));var x="",v="";if(t.cols&&t.cols.length>0){var w=t.cols,z="",T=!1,q=0,N=w.length;w[0].type==="separator"&&(x+="top ",q=1),w[w.length-1].type==="separator"&&(x+="bottom ",N-=1);for(var O=q;O<N;O++)w[O].type==="align"?(v+=G1[w[O].align],T&&(z+="none "),T=!0):w[O].type==="separator"&&T&&(z+=w[O].separator==="|"?"solid ":"dashed ",T=!1);g.setAttribute("columnalign",v.trim()),/[sd]/.test(z)&&g.setAttribute("columnlines",z.trim())}if(t.colSeparationType==="align"){for(var D=t.cols||[],G="",E=1;E<D.length;E++)G+=E%2?"0em ":"1em ";g.setAttribute("columnspacing",G.trim())}else t.colSeparationType==="alignat"||t.colSeparationType==="gather"?g.setAttribute("columnspacing","0em"):t.colSeparationType==="small"?g.setAttribute("columnspacing","0.2778em"):t.colSeparationType==="CD"?g.setAttribute("columnspacing","0.5em"):g.setAttribute("columnspacing","1em");var P="",V=t.hLinesBeforeRow;x+=V[0].length>0?"left ":"",x+=V[V.length-1].length>0?"right ":"";for(var W=1;W<V.length-1;W++)P+=V[W].length===0?"none ":V[W][0]?"dashed ":"solid ";return/[sd]/.test(P)&&g.setAttribute("rowlines",P.trim()),x!==""&&(g=new S.MathNode("menclose",[g])).setAttribute("notation",x.trim()),t.arraystretch&&t.arraystretch<1&&(g=new S.MathNode("mstyle",[g])).setAttribute("scriptlevel","1"),g},jt=function(t,e){t.envName.indexOf("ed")===-1&&Me(t);var r,a=[],n=t.envName.indexOf("at")>-1?"alignat":"align",s=t.envName==="split",h=P0(t.parser,{cols:a,addJot:!0,autoTag:s?void 0:ht(t.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:s?2:void 0,leqno:t.parser.settings.leqno},"display"),c=0,p={type:"ordgroup",mode:t.mode,body:[]};if(e[0]&&e[0].type==="ordgroup"){for(var g="",b=0;b<e[0].body.length;b++)g+=L(e[0].body[b],"textord").text;r=Number(g),c=2*r}var x=!c;h.body.forEach(function(T){for(var q=1;q<T.length;q+=2){var N=L(T[q],"styling");L(N.body[0],"ordgroup").body.unshift(p)}if(x)c<T.length&&(c=T.length);else{var O=T.length/2;if(r<O)throw new M("Too many math in a row: expected "+r+", but got "+O,T[0])}});for(var v=0;v<c;++v){var w="r",z=0;v%2==1?w="l":v>0&&x&&(z=1),a[v]={type:"align",align:w,pregap:z,postgap:0}}return h.colSeparationType=x?"align":"alignat",h};y0({type:"array",names:["array","darray"],props:{numArgs:1},handler(t,e){var r=(Be(e[0])?[e[0]]:L(e[0],"ordgroup").body).map(function(n){var s=Fe(n).text;if("lcr".indexOf(s)!==-1)return{type:"align",align:s};if(s==="|")return{type:"separator",separator:"|"};if(s===":")return{type:"separator",separator:":"};throw new M("Unknown column alignment: "+s,n)}),a={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return P0(t.parser,a,$e(t.envName))},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(t){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[t.envName.replace("*","")],r="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if(t.envName.charAt(t.envName.length-1)==="*"){var n=t.parser;if(n.consumeSpaces(),n.fetch().text==="["){if(n.consume(),n.consumeSpaces(),r=n.fetch().text,"lcr".indexOf(r)===-1)throw new M("Expected l or c or r",n.nextToken);n.consume(),n.consumeSpaces(),n.expect("]"),n.consume(),a.cols=[{type:"align",align:r}]}}var s=P0(t.parser,a,$e(t.envName)),h=Math.max(0,...s.body.map(c=>c.length));return s.cols=new Array(h).fill({type:"align",align:r}),e?{type:"leftright",mode:t.mode,body:[s],left:e[0],right:e[1],rightColor:void 0}:s},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(t){var e=P0(t.parser,{arraystretch:.5},"script");return e.colSeparationType="small",e},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["subarray"],props:{numArgs:1},handler(t,e){var r=(Be(e[0])?[e[0]]:L(e[0],"ordgroup").body).map(function(n){var s=Fe(n).text;if("lc".indexOf(s)!==-1)return{type:"align",align:s};throw new M("Unknown column alignment: "+s,n)});if(r.length>1)throw new M("{subarray} can contain only one column");var a={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((a=P0(t.parser,a,"script")).body.length>0&&a.body[0].length>1)throw new M("{subarray} can contain only one column");return a},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(t){var e=P0(t.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},$e(t.envName));return{type:"leftright",mode:t.mode,body:[e],left:t.envName.indexOf("r")>-1?".":"\\{",right:t.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:jt,htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(t){R.contains(["gather","gather*"],t.envName)&&Me(t);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:ht(t.envName),emptySingleRow:!0,leqno:t.parser.settings.leqno};return P0(t.parser,e,"display")},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:jt,htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(t){Me(t);var e={autoTag:ht(t.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:t.parser.settings.leqno};return P0(t.parser,e,"display")},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["CD"],props:{numArgs:0},handler:t=>(Me(t),function(e){var r=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){r.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();var a=e.fetch().text;if(a!=="&"&&a!=="\\\\"){if(a==="\\end"){r[r.length-1].length===0&&r.pop();break}throw new M("Expected \\\\ or \\cr or \\end",e.nextToken)}e.consume()}for(var n,s,h=[],c=[h],p=0;p<r.length;p++){for(var g=r[p],b={type:"styling",body:[],mode:"math",style:"display"},x=0;x<g.length;x++)if(Vt(g[x])){h.push(b);var v=Fe(g[x+=1]).text,w=new Array(2);if(w[0]={type:"ordgroup",mode:"math",body:[]},w[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(v)>-1)){if(!("<>AV".indexOf(v)>-1))throw new M('Expected one of "<>AV=|." after @',g[x]);for(var z=0;z<2;z++){for(var T=!0,q=x+1;q<g.length;q++){if(s=v,((n=g[q]).type==="mathord"||n.type==="atom")&&n.text===s){T=!1,x=q;break}if(Vt(g[q]))throw new M("Missing a "+v+" character to complete a CD arrow.",g[q]);w[z].body.push(g[q])}if(T)throw new M("Missing a "+v+" character to complete a CD arrow.",g[x])}}var N={type:"styling",body:[I1(v,w,e)],mode:"math",style:"display"};h.push(N),b={type:"styling",body:[],mode:"math",style:"display"}}else b.body.push(g[x]);p%2==0?h.push(b):h.shift(),h=[],c.push(h)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:"math",body:c,arraystretch:1,addJot:!0,rowGaps:[null],cols:new Array(c[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25}),colSeparationType:"CD",hLinesBeforeRow:new Array(c.length+1).fill([])}}(t.parser)),htmlBuilder:x0,mathmlBuilder:w0}),m("\\nonumber","\\gdef\\@eqnsw{0}"),m("\\notag","\\nonumber"),B({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(t,e){throw new M(t.funcName+" valid only within array environment")}});var $t=Vr;B({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(t,e){var{parser:r,funcName:a}=t,n=e[0];if(n.type!=="ordgroup")throw new M("Invalid environment name",n);for(var s="",h=0;h<n.body.length;++h)s+=L(n.body[h],"textord").text;if(a==="\\begin"){if(!$t.hasOwnProperty(s))throw new M("No such environment: "+s,n);var c=$t[s],{args:p,optArgs:g}=r.parseArguments("\\begin{"+s+"}",c),b={mode:r.mode,envName:s,parser:r},x=c.handler(b,p,g);r.expect("\\end",!1);var v=r.nextToken,w=L(r.parseFunction(),"environment");if(w.name!==s)throw new M("Mismatch: \\begin{"+s+"} matched by \\end{"+w.name+"}",v);return x}return{type:"environment",mode:r.mode,name:s,nameGroup:n}}});var Zt=(t,e)=>{var r=t.font,a=e.withFont(r);return U(t.body,a)},Kt=(t,e)=>{var r=t.font,a=e.withFont(r);return _(t.body,a)},Jt={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};B({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=Te(e[0]),s=a;return s in Jt&&(s=Jt[s]),{type:"font",mode:r.mode,font:s.slice(1),body:n}},htmlBuilder:Zt,mathmlBuilder:Kt}),B({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(t,e)=>{var{parser:r}=t,a=e[0],n=R.isCharacterBox(a);return{type:"mclass",mode:r.mode,mclass:Se(a),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:a}],isCharacterBox:n}}}),B({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{var{parser:r,funcName:a,breakOnTokenText:n}=t,{mode:s}=r,h=r.parseExpression(!0,n);return{type:"font",mode:s,font:"math"+a.slice(1),body:{type:"ordgroup",mode:r.mode,body:h}}},htmlBuilder:Zt,mathmlBuilder:Kt});var Fr=(t,e)=>{var r=e;return t==="display"?r=r.id>=I.SCRIPT.id?r.text():I.DISPLAY:t==="text"&&r.size===I.DISPLAY.size?r=I.TEXT:t==="script"?r=I.SCRIPT:t==="scriptscript"&&(r=I.SCRIPTSCRIPT),r},mt=(t,e)=>{var r,a=Fr(t.size,e.style),n=a.fracNum(),s=a.fracDen();r=e.havingStyle(n);var h=U(t.numer,r,e);if(t.continued){var c=8.5/e.fontMetrics().ptPerEm,p=3.5/e.fontMetrics().ptPerEm;h.height=h.height<c?c:h.height,h.depth=h.depth<p?p:h.depth}r=e.havingStyle(s);var g,b,x,v,w,z,T,q,N,O,D=U(t.denom,r,e);if(t.hasBarLine?(t.barSize?(b=Q(t.barSize,e),g=y.makeLineSpan("frac-line",e,b)):g=y.makeLineSpan("frac-line",e),b=g.height,x=g.height):(g=null,b=0,x=e.fontMetrics().defaultRuleThickness),a.size===I.DISPLAY.size||t.size==="display"?(v=e.fontMetrics().num1,w=b>0?3*x:7*x,z=e.fontMetrics().denom1):(b>0?(v=e.fontMetrics().num2,w=x):(v=e.fontMetrics().num3,w=3*x),z=e.fontMetrics().denom2),g){var G=e.fontMetrics().axisHeight;v-h.depth-(G+.5*b)<w&&(v+=w-(v-h.depth-(G+.5*b))),G-.5*b-(D.height-z)<w&&(z+=w-(G-.5*b-(D.height-z)));var E=-(G-.5*b);T=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:D,shift:z},{type:"elem",elem:g,shift:E},{type:"elem",elem:h,shift:-v}]},e)}else{var P=v-h.depth-(D.height-z);P<w&&(v+=.5*(w-P),z+=.5*(w-P)),T=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:D,shift:z},{type:"elem",elem:h,shift:-v}]},e)}return r=e.havingStyle(a),T.height*=r.sizeMultiplier/e.sizeMultiplier,T.depth*=r.sizeMultiplier/e.sizeMultiplier,q=a.size===I.DISPLAY.size?e.fontMetrics().delim1:a.size===I.SCRIPTSCRIPT.size?e.havingStyle(I.SCRIPT).fontMetrics().delim2:e.fontMetrics().delim2,N=t.leftDelim==null?ee(e,["mopen"]):R0.customSizedDelim(t.leftDelim,q,!0,e.havingStyle(a),t.mode,["mopen"]),O=t.continued?y.makeSpan([]):t.rightDelim==null?ee(e,["mclose"]):R0.customSizedDelim(t.rightDelim,q,!0,e.havingStyle(a),t.mode,["mclose"]),y.makeSpan(["mord"].concat(r.sizingClasses(e)),[N,y.makeSpan(["mfrac"],[T]),O],e)},ct=(t,e)=>{var r=new S.MathNode("mfrac",[_(t.numer,e),_(t.denom,e)]);if(t.hasBarLine){if(t.barSize){var a=Q(t.barSize,e);r.setAttribute("linethickness",A(a))}}else r.setAttribute("linethickness","0px");var n=Fr(t.size,e.style);if(n.size!==e.style.size){r=new S.MathNode("mstyle",[r]);var s=n.size===I.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",s),r.setAttribute("scriptlevel","0")}if(t.leftDelim!=null||t.rightDelim!=null){var h=[];if(t.leftDelim!=null){var c=new S.MathNode("mo",[new S.TextNode(t.leftDelim.replace("\\",""))]);c.setAttribute("fence","true"),h.push(c)}if(h.push(r),t.rightDelim!=null){var p=new S.MathNode("mo",[new S.TextNode(t.rightDelim.replace("\\",""))]);p.setAttribute("fence","true"),h.push(p)}return vt(h)}return r};B({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(t,e)=>{var r,{parser:a,funcName:n}=t,s=e[0],h=e[1],c=null,p=null,g="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,c="(",p=")";break;case"\\\\bracefrac":r=!1,c="\\{",p="\\}";break;case"\\\\brackfrac":r=!1,c="[",p="]";break;default:throw new Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":g="display";break;case"\\tfrac":case"\\tbinom":g="text"}return{type:"genfrac",mode:a.mode,continued:!1,numer:s,denom:h,hasBarLine:r,leftDelim:c,rightDelim:p,size:g,barSize:null}},htmlBuilder:mt,mathmlBuilder:ct}),B({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=e[0],s=e[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:s,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),B({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(t){var e,{parser:r,funcName:a,token:n}=t;switch(a){case"\\over":e="\\frac";break;case"\\choose":e="\\binom";break;case"\\atop":e="\\\\atopfrac";break;case"\\brace":e="\\\\bracefrac";break;case"\\brack":e="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:e,token:n}}});var Qt=["display","text","script","scriptscript"],er=function(t){var e=null;return t.length>0&&(e=(e=t)==="."?null:e),e};B({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(t,e){var r,{parser:a}=t,n=e[4],s=e[5],h=Te(e[0]),c=h.type==="atom"&&h.family==="open"?er(h.text):null,p=Te(e[1]),g=p.type==="atom"&&p.family==="close"?er(p.text):null,b=L(e[2],"size"),x=null;r=!!b.isBlank||(x=b.value).number>0;var v="auto",w=e[3];if(w.type==="ordgroup"){if(w.body.length>0){var z=L(w.body[0],"textord");v=Qt[Number(z.text)]}}else w=L(w,"textord"),v=Qt[Number(w.text)];return{type:"genfrac",mode:a.mode,numer:n,denom:s,continued:!1,hasBarLine:r,barSize:x,leftDelim:c,rightDelim:g,size:v}},htmlBuilder:mt,mathmlBuilder:ct}),B({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(t,e){var{parser:r,funcName:a,token:n}=t;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:L(e[0],"size").value,token:n}}}),B({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=e[0],s=function(p){if(!p)throw new Error("Expected non-null, but got "+String(p));return p}(L(e[1],"infix").size),h=e[2],c=s.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:h,continued:!1,hasBarLine:c,barSize:s,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:mt,mathmlBuilder:ct});var Gr=(t,e)=>{var r,a,n=e.style;t.type==="supsub"?(r=t.sup?U(t.sup,e.havingStyle(n.sup()),e):U(t.sub,e.havingStyle(n.sub()),e),a=L(t.base,"horizBrace")):a=L(t,"horizBrace");var s,h=U(a.base,e.havingBaseStyle(I.DISPLAY)),c=qe(a,e);if(a.isOver?(s=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:h},{type:"kern",size:.1},{type:"elem",elem:c}]},e)).children[0].children[0].children[1].classes.push("svg-align"):(s=y.makeVList({positionType:"bottom",positionData:h.depth+.1+c.height,children:[{type:"elem",elem:c},{type:"kern",size:.1},{type:"elem",elem:h}]},e)).children[0].children[0].children[0].classes.push("svg-align"),r){var p=y.makeSpan(["mord",a.isOver?"mover":"munder"],[s],e);s=a.isOver?y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:p},{type:"kern",size:.2},{type:"elem",elem:r}]},e):y.makeVList({positionType:"bottom",positionData:p.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:p}]},e)}return y.makeSpan(["mord",a.isOver?"mover":"munder"],[s],e)};B({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(t,e){var{parser:r,funcName:a}=t;return{type:"horizBrace",mode:r.mode,label:a,isOver:/^\\over/.test(a),base:e[0]}},htmlBuilder:Gr,mathmlBuilder:(t,e)=>{var r=Ce(t.label);return new S.MathNode(t.isOver?"mover":"munder",[_(t.base,e),r])}}),B({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(t,e)=>{var{parser:r}=t,a=e[1],n=L(e[0],"url").url;return r.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:r.mode,href:n,body:t0(a)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(t,e)=>{var r=a0(t.body,e,!1);return y.makeAnchor(t.href,[],r,e)},mathmlBuilder:(t,e)=>{var r=G0(t.body,e);return r instanceof u0||(r=new u0("mrow",[r])),r.setAttribute("href",t.href),r}}),B({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(t,e)=>{var{parser:r}=t,a=L(e[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:a}))return r.formatUnsupportedCmd("\\url");for(var n=[],s=0;s<a.length;s++){var h=a[s];h==="~"&&(h="\\textasciitilde"),n.push({type:"textord",mode:"text",text:h})}var c={type:"text",mode:r.mode,font:"\\texttt",body:n};return{type:"href",mode:r.mode,href:a,body:t0(c)}}}),B({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(t,e){var{parser:r}=t;return{type:"hbox",mode:r.mode,body:t0(e[0])}},htmlBuilder(t,e){var r=a0(t.body,e,!1);return y.makeFragment(r)},mathmlBuilder:(t,e)=>new S.MathNode("mrow",m0(t.body,e))}),B({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(t,e)=>{var r,{parser:a,funcName:n,token:s}=t,h=L(e[0],"raw").string,c=e[1];a.settings.strict&&a.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var p={};switch(n){case"\\htmlClass":p.class=h,r={command:"\\htmlClass",class:h};break;case"\\htmlId":p.id=h,r={command:"\\htmlId",id:h};break;case"\\htmlStyle":p.style=h,r={command:"\\htmlStyle",style:h};break;case"\\htmlData":for(var g=h.split(","),b=0;b<g.length;b++){var x=g[b].split("=");if(x.length!==2)throw new M("Error parsing key-value for \\htmlData");p["data-"+x[0].trim()]=x[1].trim()}r={command:"\\htmlData",attributes:p};break;default:throw new Error("Unrecognized html command")}return a.settings.isTrusted(r)?{type:"html",mode:a.mode,attributes:p,body:t0(c)}:a.formatUnsupportedCmd(n)},htmlBuilder:(t,e)=>{var r=a0(t.body,e,!1),a=["enclosing"];t.attributes.class&&a.push(...t.attributes.class.trim().split(/\s+/));var n=y.makeSpan(a,r,e);for(var s in t.attributes)s!=="class"&&t.attributes.hasOwnProperty(s)&&n.setAttribute(s,t.attributes[s]);return n},mathmlBuilder:(t,e)=>G0(t.body,e)}),B({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(t,e)=>{var{parser:r}=t;return{type:"htmlmathml",mode:r.mode,html:t0(e[0]),mathml:t0(e[1])}},htmlBuilder:(t,e)=>{var r=a0(t.html,e,!1);return y.makeFragment(r)},mathmlBuilder:(t,e)=>G0(t.mathml,e)});var Ze=function(t){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(t))return{number:+t,unit:"bp"};var e=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t);if(!e)throw new M("Invalid size: '"+t+"' in \\includegraphics");var r={number:+(e[1]+e[2]),unit:e[3]};if(!br(r))throw new M("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};B({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(t,e,r)=>{var{parser:a}=t,n={number:0,unit:"em"},s={number:.9,unit:"em"},h={number:0,unit:"em"},c="";if(r[0])for(var p=L(r[0],"raw").string.split(","),g=0;g<p.length;g++){var b=p[g].split("=");if(b.length===2){var x=b[1].trim();switch(b[0].trim()){case"alt":c=x;break;case"width":n=Ze(x);break;case"height":s=Ze(x);break;case"totalheight":h=Ze(x);break;default:throw new M("Invalid key: '"+b[0]+"' in \\includegraphics.")}}}var v=L(e[0],"url").url;return c===""&&(c=(c=(c=v).replace(/^.*[\\/]/,"")).substring(0,c.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:v})?{type:"includegraphics",mode:a.mode,alt:c,width:n,height:s,totalheight:h,src:v}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(t,e)=>{var r=Q(t.height,e),a=0;t.totalheight.number>0&&(a=Q(t.totalheight,e)-r);var n=0;t.width.number>0&&(n=Q(t.width,e));var s={height:A(r+a)};n>0&&(s.width=A(n)),a>0&&(s.verticalAlign=A(-a));var h=new d1(t.src,t.alt,s);return h.height=r,h.depth=a,h},mathmlBuilder:(t,e)=>{var r=new S.MathNode("mglyph",[]);r.setAttribute("alt",t.alt);var a=Q(t.height,e),n=0;if(t.totalheight.number>0&&(n=Q(t.totalheight,e)-a,r.setAttribute("valign",A(-n))),r.setAttribute("height",A(a+n)),t.width.number>0){var s=Q(t.width,e);r.setAttribute("width",A(s))}return r.setAttribute("src",t.src),r}}),B({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(t,e){var{parser:r,funcName:a}=t,n=L(e[0],"size");if(r.settings.strict){var s=a[1]==="m",h=n.value.unit==="mu";s?(h||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, not "+n.value.unit+" units"),r.mode!=="math"&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):h&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:n.value}},htmlBuilder:(t,e)=>y.makeGlue(t.dimension,e),mathmlBuilder(t,e){var r=Q(t.dimension,e);return new S.SpaceNode(r)}}),B({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=e[0];return{type:"lap",mode:r.mode,alignment:a.slice(5),body:n}},htmlBuilder:(t,e)=>{var r;t.alignment==="clap"?(r=y.makeSpan([],[U(t.body,e)]),r=y.makeSpan(["inner"],[r],e)):r=y.makeSpan(["inner"],[U(t.body,e)]);var a=y.makeSpan(["fix"],[]),n=y.makeSpan([t.alignment],[r,a],e),s=y.makeSpan(["strut"]);return s.style.height=A(n.height+n.depth),n.depth&&(s.style.verticalAlign=A(-n.depth)),n.children.unshift(s),n=y.makeSpan(["thinbox"],[n],e),y.makeSpan(["mord","vbox"],[n],e)},mathmlBuilder:(t,e)=>{var r=new S.MathNode("mpadded",[_(t.body,e)]);if(t.alignment!=="rlap"){var a=t.alignment==="llap"?"-1":"-0.5";r.setAttribute("lspace",a+"width")}return r.setAttribute("width","0px"),r}}),B({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){var{funcName:r,parser:a}=t,n=a.mode;a.switchMode("math");var s=r==="\\("?"\\)":"$",h=a.parseExpression(!1,s);return a.expect(s),a.switchMode(n),{type:"styling",mode:a.mode,style:"text",body:h}}}),B({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){throw new M("Mismatched "+t.funcName)}});var tr=(t,e)=>{switch(e.style.size){case I.DISPLAY.size:return t.display;case I.TEXT.size:return t.text;case I.SCRIPT.size:return t.script;case I.SCRIPTSCRIPT.size:return t.scriptscript;default:return t.text}};B({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(t,e)=>{var{parser:r}=t;return{type:"mathchoice",mode:r.mode,display:t0(e[0]),text:t0(e[1]),script:t0(e[2]),scriptscript:t0(e[3])}},htmlBuilder:(t,e)=>{var r=tr(t,e),a=a0(r,e,!1);return y.makeFragment(a)},mathmlBuilder:(t,e)=>{var r=tr(t,e);return G0(r,e)}});var Ur=(t,e,r,a,n,s,h)=>{t=y.makeSpan([],[t]);var c,p,g,b=r&&R.isCharacterBox(r);if(e){var x=U(e,a.havingStyle(n.sup()),a);p={elem:x,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-x.depth)}}if(r){var v=U(r,a.havingStyle(n.sub()),a);c={elem:v,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-v.height)}}if(p&&c){var w=a.fontMetrics().bigOpSpacing5+c.elem.height+c.elem.depth+c.kern+t.depth+h;g=y.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:A(-s)},{type:"kern",size:c.kern},{type:"elem",elem:t},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:A(s)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(c){var z=t.height-h;g=y.makeVList({positionType:"top",positionData:z,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:A(-s)},{type:"kern",size:c.kern},{type:"elem",elem:t}]},a)}else{if(!p)return t;var T=t.depth+h;g=y.makeVList({positionType:"bottom",positionData:T,children:[{type:"elem",elem:t},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:A(s)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}var q=[g];if(c&&s!==0&&!b){var N=y.makeSpan(["mspace"],[],a);N.style.marginRight=A(s),q.unshift(N)}return y.makeSpan(["mop","op-limits"],q,a)},Yr=["\\smallint"],K0=(t,e)=>{var r,a,n,s=!1;t.type==="supsub"?(r=t.sup,a=t.sub,n=L(t.base,"op"),s=!0):n=L(t,"op");var h,c=e.style,p=!1;if(c.size===I.DISPLAY.size&&n.symbol&&!R.contains(Yr,n.name)&&(p=!0),n.symbol){var g=p?"Size2-Regular":"Size1-Regular",b="";if(n.name!=="\\oiint"&&n.name!=="\\oiiint"||(b=n.name.slice(1),n.name=b==="oiint"?"\\iint":"\\iiint"),h=y.makeSymbol(n.name,g,"math",e,["mop","op-symbol",p?"large-op":"small-op"]),b.length>0){var x=h.italic,v=y.staticSvg(b+"Size"+(p?"2":"1"),e);h=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:h,shift:0},{type:"elem",elem:v,shift:p?.08:0}]},e),n.name="\\"+b,h.classes.unshift("mop"),h.italic=x}}else if(n.body){var w=a0(n.body,e,!0);w.length===1&&w[0]instanceof f0?(h=w[0]).classes[0]="mop":h=y.makeSpan(["mop"],w,e)}else{for(var z=[],T=1;T<n.name.length;T++)z.push(y.mathsym(n.name[T],n.mode,e));h=y.makeSpan(["mop"],z,e)}var q=0,N=0;return(h instanceof f0||n.name==="\\oiint"||n.name==="\\oiiint")&&!n.suppressBaseShift&&(q=(h.height-h.depth)/2-e.fontMetrics().axisHeight,N=h.italic),s?Ur(h,r,a,e,c,N,q):(q&&(h.style.position="relative",h.style.top=A(q)),h)},te=(t,e)=>{var r;if(t.symbol)r=new u0("mo",[d0(t.name,t.mode)]),R.contains(Yr,t.name)&&r.setAttribute("largeop","false");else if(t.body)r=new u0("mo",m0(t.body,e));else{r=new u0("mi",[new M0(t.name.slice(1))]);var a=new u0("mo",[d0("⁡","text")]);r=t.parentIsSupSub?new u0("mrow",[r,a]):Tr([r,a])}return r},U1={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};B({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=a;return n.length===1&&(n=U1[n]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:K0,mathmlBuilder:te}),B({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{var{parser:r}=t,a=e[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:t0(a)}},htmlBuilder:K0,mathmlBuilder:te});var Y1={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};B({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(t){var{parser:e,funcName:r}=t;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:K0,mathmlBuilder:te}),B({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(t){var{parser:e,funcName:r}=t;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:K0,mathmlBuilder:te}),B({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(t){var{parser:e,funcName:r}=t,a=r;return a.length===1&&(a=Y1[a]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:K0,mathmlBuilder:te});var Xr=(t,e)=>{var r,a,n,s,h=!1;if(t.type==="supsub"?(r=t.sup,a=t.sub,n=L(t.base,"operatorname"),h=!0):n=L(t,"operatorname"),n.body.length>0){for(var c=n.body.map(x=>{var v=x.text;return typeof v=="string"?{type:"textord",mode:x.mode,text:v}:x}),p=a0(c,e.withFont("mathrm"),!0),g=0;g<p.length;g++){var b=p[g];b instanceof f0&&(b.text=b.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}s=y.makeSpan(["mop"],p,e)}else s=y.makeSpan(["mop"],[],e);return h?Ur(s,r,a,e,e.style,0,0):s};function _r(t,e,r){for(var a=a0(t,e,!1),n=e.sizeMultiplier/r.sizeMultiplier,s=0;s<a.length;s++){var h=a[s].classes.indexOf("sizing");h<0?Array.prototype.push.apply(a[s].classes,e.sizingClasses(r)):a[s].classes[h+1]==="reset-size"+e.size&&(a[s].classes[h+1]="reset-size"+r.size),a[s].height*=n,a[s].depth*=n}return y.makeFragment(a)}B({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(t,e)=>{var{parser:r,funcName:a}=t,n=e[0];return{type:"operatorname",mode:r.mode,body:t0(n),alwaysHandleSupSub:a==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Xr,mathmlBuilder:(t,e)=>{for(var r=m0(t.body,e.withFont("mathrm")),a=!0,n=0;n<r.length;n++){var s=r[n];if(!(s instanceof S.SpaceNode))if(s instanceof S.MathNode)switch(s.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var h=s.children[0];s.children.length===1&&h instanceof S.TextNode?h.text=h.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break;default:a=!1}else a=!1}if(a){var c=r.map(b=>b.toText()).join("");r=[new S.TextNode(c)]}var p=new S.MathNode("mi",r);p.setAttribute("mathvariant","normal");var g=new S.MathNode("mo",[d0("⁡","text")]);return t.parentIsSupSub?new S.MathNode("mrow",[p,g]):S.newDocumentFragment([p,g])}}),m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),W0({type:"ordgroup",htmlBuilder:(t,e)=>t.semisimple?y.makeFragment(a0(t.body,e,!1)):y.makeSpan(["mord"],a0(t.body,e,!0),e),mathmlBuilder:(t,e)=>G0(t.body,e,!0)}),B({type:"overline",names:["\\overline"],props:{numArgs:1},handler(t,e){var{parser:r}=t,a=e[0];return{type:"overline",mode:r.mode,body:a}},htmlBuilder(t,e){var r=U(t.body,e.havingCrampedStyle()),a=y.makeLineSpan("overline-line",e),n=e.fontMetrics().defaultRuleThickness,s=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:a},{type:"kern",size:n}]},e);return y.makeSpan(["mord","overline"],[s],e)},mathmlBuilder(t,e){var r=new S.MathNode("mo",[new S.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new S.MathNode("mover",[_(t.body,e),r]);return a.setAttribute("accent","true"),a}}),B({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{var{parser:r}=t,a=e[0];return{type:"phantom",mode:r.mode,body:t0(a)}},htmlBuilder:(t,e)=>{var r=a0(t.body,e.withPhantom(),!1);return y.makeFragment(r)},mathmlBuilder:(t,e)=>{var r=m0(t.body,e);return new S.MathNode("mphantom",r)}}),B({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{var{parser:r}=t,a=e[0];return{type:"hphantom",mode:r.mode,body:a}},htmlBuilder:(t,e)=>{var r=y.makeSpan([],[U(t.body,e.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var a=0;a<r.children.length;a++)r.children[a].height=0,r.children[a].depth=0;return r=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e),y.makeSpan(["mord"],[r],e)},mathmlBuilder:(t,e)=>{var r=m0(t0(t.body),e),a=new S.MathNode("mphantom",r),n=new S.MathNode("mpadded",[a]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}}),B({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{var{parser:r}=t,a=e[0];return{type:"vphantom",mode:r.mode,body:a}},htmlBuilder:(t,e)=>{var r=y.makeSpan(["inner"],[U(t.body,e.withPhantom())]),a=y.makeSpan(["fix"],[]);return y.makeSpan(["mord","rlap"],[r,a],e)},mathmlBuilder:(t,e)=>{var r=m0(t0(t.body),e),a=new S.MathNode("mphantom",r),n=new S.MathNode("mpadded",[a]);return n.setAttribute("width","0px"),n}}),B({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(t,e){var{parser:r}=t,a=L(e[0],"size").value,n=e[1];return{type:"raisebox",mode:r.mode,dy:a,body:n}},htmlBuilder(t,e){var r=U(t.body,e),a=Q(t.dy,e);return y.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:r}]},e)},mathmlBuilder(t,e){var r=new S.MathNode("mpadded",[_(t.body,e)]),a=t.dy.number+t.dy.unit;return r.setAttribute("voffset",a),r}}),B({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(t){var{parser:e}=t;return{type:"internal",mode:e.mode}}}),B({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(t,e,r){var{parser:a}=t,n=r[0],s=L(e[0],"size"),h=L(e[1],"size");return{type:"rule",mode:a.mode,shift:n&&L(n,"size").value,width:s.value,height:h.value}},htmlBuilder(t,e){var r=y.makeSpan(["mord","rule"],[],e),a=Q(t.width,e),n=Q(t.height,e),s=t.shift?Q(t.shift,e):0;return r.style.borderRightWidth=A(a),r.style.borderTopWidth=A(n),r.style.bottom=A(s),r.width=a,r.height=n+s,r.depth=-s,r.maxFontSize=1.125*n*e.sizeMultiplier,r},mathmlBuilder(t,e){var r=Q(t.width,e),a=Q(t.height,e),n=t.shift?Q(t.shift,e):0,s=e.color&&e.getColor()||"black",h=new S.MathNode("mspace");h.setAttribute("mathbackground",s),h.setAttribute("width",A(r)),h.setAttribute("height",A(a));var c=new S.MathNode("mpadded",[h]);return n>=0?c.setAttribute("height",A(n)):(c.setAttribute("height",A(n)),c.setAttribute("depth",A(-n))),c.setAttribute("voffset",A(n)),c}});var rr=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];B({type:"sizing",names:rr,props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{var{breakOnTokenText:r,funcName:a,parser:n}=t,s=n.parseExpression(!1,r);return{type:"sizing",mode:n.mode,size:rr.indexOf(a)+1,body:s}},htmlBuilder:(t,e)=>{var r=e.havingSize(t.size);return _r(t.body,r,e)},mathmlBuilder:(t,e)=>{var r=e.havingSize(t.size),a=m0(t.body,r),n=new S.MathNode("mstyle",a);return n.setAttribute("mathsize",A(r.sizeMultiplier)),n}}),B({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(t,e,r)=>{var{parser:a}=t,n=!1,s=!1,h=r[0]&&L(r[0],"ordgroup");if(h)for(var c="",p=0;p<h.body.length;++p)if((c=h.body[p].text)==="t")n=!0;else{if(c!=="b"){n=!1,s=!1;break}s=!0}else n=!0,s=!0;var g=e[0];return{type:"smash",mode:a.mode,body:g,smashHeight:n,smashDepth:s}},htmlBuilder:(t,e)=>{var r=y.makeSpan([],[U(t.body,e)]);if(!t.smashHeight&&!t.smashDepth)return r;if(t.smashHeight&&(r.height=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].height=0;if(t.smashDepth&&(r.depth=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].depth=0;var s=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e);return y.makeSpan(["mord"],[s],e)},mathmlBuilder:(t,e)=>{var r=new S.MathNode("mpadded",[_(t.body,e)]);return t.smashHeight&&r.setAttribute("height","0px"),t.smashDepth&&r.setAttribute("depth","0px"),r}}),B({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,r){var{parser:a}=t,n=r[0],s=e[0];return{type:"sqrt",mode:a.mode,body:s,index:n}},htmlBuilder(t,e){var r=U(t.body,e.havingCrampedStyle());r.height===0&&(r.height=e.fontMetrics().xHeight),r=y.wrapFragment(r,e);var a=e.fontMetrics().defaultRuleThickness,n=a;e.style.id<I.TEXT.id&&(n=e.fontMetrics().xHeight);var s=a+n/4,h=r.height+r.depth+s+a,{span:c,ruleWidth:p,advanceWidth:g}=R0.sqrtImage(h,e),b=c.height-p;b>r.height+r.depth+s&&(s=(s+b-r.height-r.depth)/2);var x=c.height-r.height-s-p;r.style.paddingLeft=A(g);var v=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+x)},{type:"elem",elem:c},{type:"kern",size:p}]},e);if(t.index){var w=e.havingStyle(I.SCRIPTSCRIPT),z=U(t.index,w,e),T=.6*(v.height-v.depth),q=y.makeVList({positionType:"shift",positionData:-T,children:[{type:"elem",elem:z}]},e),N=y.makeSpan(["root"],[q]);return y.makeSpan(["mord","sqrt"],[N,v],e)}return y.makeSpan(["mord","sqrt"],[v],e)},mathmlBuilder(t,e){var{body:r,index:a}=t;return a?new S.MathNode("mroot",[_(r,e),_(a,e)]):new S.MathNode("msqrt",[_(r,e)])}});var ar={display:I.DISPLAY,text:I.TEXT,script:I.SCRIPT,scriptscript:I.SCRIPTSCRIPT};B({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t,e){var{breakOnTokenText:r,funcName:a,parser:n}=t,s=n.parseExpression(!0,r),h=a.slice(1,a.length-5);return{type:"styling",mode:n.mode,style:h,body:s}},htmlBuilder(t,e){var r=ar[t.style],a=e.havingStyle(r).withFont("");return _r(t.body,a,e)},mathmlBuilder(t,e){var r=ar[t.style],a=e.havingStyle(r),n=m0(t.body,a),s=new S.MathNode("mstyle",n),h={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[t.style];return s.setAttribute("scriptlevel",h[0]),s.setAttribute("displaystyle",h[1]),s}});W0({type:"supsub",htmlBuilder(t,e){var r=function(F,n0){var K=F.base;return K?K.type==="op"?K.limits&&(n0.style.size===I.DISPLAY.size||K.alwaysHandleSupSub)?K0:null:K.type==="operatorname"?K.alwaysHandleSupSub&&(n0.style.size===I.DISPLAY.size||K.limits)?Xr:null:K.type==="accent"?R.isCharacterBox(K.base)?lt:null:K.type==="horizBrace"&&!F.sub===K.isOver?Gr:null:null}(t,e);if(r)return r(t,e);var a,n,s,{base:h,sup:c,sub:p}=t,g=U(h,e),b=e.fontMetrics(),x=0,v=0,w=h&&R.isCharacterBox(h);if(c){var z=e.havingStyle(e.style.sup());a=U(c,z,e),w||(x=g.height-z.fontMetrics().supDrop*z.sizeMultiplier/e.sizeMultiplier)}if(p){var T=e.havingStyle(e.style.sub());n=U(p,T,e),w||(v=g.depth+T.fontMetrics().subDrop*T.sizeMultiplier/e.sizeMultiplier)}s=e.style===I.DISPLAY?b.sup1:e.style.cramped?b.sup3:b.sup2;var q,N=e.sizeMultiplier,O=A(.5/b.ptPerEm/N),D=null;if(n){var G=t.base&&t.base.type==="op"&&t.base.name&&(t.base.name==="\\oiint"||t.base.name==="\\oiiint");(g instanceof f0||G)&&(D=A(-g.italic))}if(a&&n){x=Math.max(x,s,a.depth+.25*b.xHeight),v=Math.max(v,b.sub2);var E=4*b.defaultRuleThickness;if(x-a.depth-(n.height-v)<E){v=E-(x-a.depth)+n.height;var P=.8*b.xHeight-(x-a.depth);P>0&&(x+=P,v-=P)}var V=[{type:"elem",elem:n,shift:v,marginRight:O,marginLeft:D},{type:"elem",elem:a,shift:-x,marginRight:O}];q=y.makeVList({positionType:"individualShift",children:V},e)}else if(n){v=Math.max(v,b.sub1,n.height-.8*b.xHeight);var W=[{type:"elem",elem:n,marginLeft:D,marginRight:O}];q=y.makeVList({positionType:"shift",positionData:v,children:W},e)}else{if(!a)throw new Error("supsub must have either sup or sub.");x=Math.max(x,s,a.depth+.25*b.xHeight),q=y.makeVList({positionType:"shift",positionData:-x,children:[{type:"elem",elem:a,marginRight:O}]},e)}var j=it(g,"right")||"mord";return y.makeSpan([j],[g,y.makeSpan(["msupsub"],[q])],e)},mathmlBuilder(t,e){var r,a=!1;t.base&&t.base.type==="horizBrace"&&!!t.sup===t.base.isOver&&(a=!0,r=t.base.isOver),!t.base||t.base.type!=="op"&&t.base.type!=="operatorname"||(t.base.parentIsSupSub=!0);var n,s=[_(t.base,e)];if(t.sub&&s.push(_(t.sub,e)),t.sup&&s.push(_(t.sup,e)),a)n=r?"mover":"munder";else if(t.sub)if(t.sup){var h=t.base;n=h&&h.type==="op"&&h.limits&&e.style===I.DISPLAY||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(e.style===I.DISPLAY||h.limits)?"munderover":"msubsup"}else{var c=t.base;n=c&&c.type==="op"&&c.limits&&(e.style===I.DISPLAY||c.alwaysHandleSupSub)||c&&c.type==="operatorname"&&c.alwaysHandleSupSub&&(c.limits||e.style===I.DISPLAY)?"munder":"msub"}else{var p=t.base;n=p&&p.type==="op"&&p.limits&&(e.style===I.DISPLAY||p.alwaysHandleSupSub)||p&&p.type==="operatorname"&&p.alwaysHandleSupSub&&(p.limits||e.style===I.DISPLAY)?"mover":"msup"}return new S.MathNode(n,s)}}),W0({type:"atom",htmlBuilder:(t,e)=>y.mathsym(t.text,t.mode,e,["m"+t.family]),mathmlBuilder(t,e){var r=new S.MathNode("mo",[d0(t.text,t.mode)]);if(t.family==="bin"){var a=st(t,e);a==="bold-italic"&&r.setAttribute("mathvariant",a)}else t.family==="punct"?r.setAttribute("separator","true"):t.family!=="open"&&t.family!=="close"||r.setAttribute("stretchy","false");return r}});var nr={mi:"italic",mn:"normal",mtext:"normal"};W0({type:"mathord",htmlBuilder:(t,e)=>y.makeOrd(t,e,"mathord"),mathmlBuilder(t,e){var r=new S.MathNode("mi",[d0(t.text,t.mode,e)]),a=st(t,e)||"italic";return a!==nr[r.type]&&r.setAttribute("mathvariant",a),r}}),W0({type:"textord",htmlBuilder:(t,e)=>y.makeOrd(t,e,"textord"),mathmlBuilder(t,e){var r,a=d0(t.text,t.mode,e),n=st(t,e)||"normal";return r=t.mode==="text"?new S.MathNode("mtext",[a]):/[0-9]/.test(t.text)?new S.MathNode("mn",[a]):t.text==="\\prime"?new S.MathNode("mo",[a]):new S.MathNode("mi",[a]),n!==nr[r.type]&&r.setAttribute("mathvariant",n),r}});var Ke={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Je={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};W0({type:"spacing",htmlBuilder(t,e){if(Je.hasOwnProperty(t.text)){var r=Je[t.text].className||"";if(t.mode==="text"){var a=y.makeOrd(t,e,"textord");return a.classes.push(r),a}return y.makeSpan(["mspace",r],[y.mathsym(t.text,t.mode,e)],e)}if(Ke.hasOwnProperty(t.text))return y.makeSpan(["mspace",Ke[t.text]],[],e);throw new M('Unknown type of space "'+t.text+'"')},mathmlBuilder(t,e){if(!Je.hasOwnProperty(t.text)){if(Ke.hasOwnProperty(t.text))return new S.MathNode("mspace");throw new M('Unknown type of space "'+t.text+'"')}return new S.MathNode("mtext",[new S.TextNode(" ")])}});var ir=()=>{var t=new S.MathNode("mtd",[]);return t.setAttribute("width","50%"),t};W0({type:"tag",mathmlBuilder(t,e){var r=new S.MathNode("mtable",[new S.MathNode("mtr",[ir(),new S.MathNode("mtd",[G0(t.body,e)]),ir(),new S.MathNode("mtd",[G0(t.tag,e)])])]);return r.setAttribute("width","100%"),r}});var or={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},sr={"\\textbf":"textbf","\\textmd":"textmd"},X1={"\\textit":"textit","\\textup":"textup"},lr=(t,e)=>{var r=t.font;return r?or[r]?e.withTextFontFamily(or[r]):sr[r]?e.withTextFontWeight(sr[r]):r==="\\emph"?e.fontShape==="textit"?e.withTextFontShape("textup"):e.withTextFontShape("textit"):e.withTextFontShape(X1[r]):e};B({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(t,e){var{parser:r,funcName:a}=t,n=e[0];return{type:"text",mode:r.mode,body:t0(n),font:a}},htmlBuilder(t,e){var r=lr(t,e),a=a0(t.body,r,!0);return y.makeSpan(["mord","text"],a,r)},mathmlBuilder(t,e){var r=lr(t,e);return G0(t.body,r)}}),B({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(t,e){var{parser:r}=t;return{type:"underline",mode:r.mode,body:e[0]}},htmlBuilder(t,e){var r=U(t.body,e),a=y.makeLineSpan("underline-line",e),n=e.fontMetrics().defaultRuleThickness,s=y.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n},{type:"elem",elem:a},{type:"kern",size:3*n},{type:"elem",elem:r}]},e);return y.makeSpan(["mord","underline"],[s],e)},mathmlBuilder(t,e){var r=new S.MathNode("mo",[new S.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new S.MathNode("munder",[_(t.body,e),r]);return a.setAttribute("accentunder","true"),a}}),B({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(t,e){var{parser:r}=t;return{type:"vcenter",mode:r.mode,body:e[0]}},htmlBuilder(t,e){var r=U(t.body,e),a=e.fontMetrics().axisHeight,n=.5*(r.height-a-(r.depth+a));return y.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:r}]},e)},mathmlBuilder:(t,e)=>new S.MathNode("mpadded",[_(t.body,e)],["vcenter"])}),B({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(t,e,r){throw new M("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(t,e){for(var r=hr(t),a=[],n=e.havingStyle(e.style.text()),s=0;s<r.length;s++){var h=r[s];h==="~"&&(h="\\textasciitilde"),a.push(y.makeSymbol(h,"Typewriter-Regular",t.mode,n,["mord","texttt"]))}return y.makeSpan(["mord","text"].concat(n.sizingClasses(e)),y.tryCombineChars(a),n)},mathmlBuilder(t,e){var r=new S.TextNode(hr(t)),a=new S.MathNode("mtext",[r]);return a.setAttribute("mathvariant","monospace"),a}});var hr=t=>t.body.replace(/ /g,t.star?"␣":" "),F0=zr,Wr=`[ \r
	]`,_1="(\\\\[a-zA-Z@]+)"+Wr+"*",pt="[̀-ͯ]",W1=new RegExp(pt+"+$"),j1="("+Wr+`+)|\\\\(
|[ \r	]+
?)[ \r	]*|([!-\\[\\]-‧‪-퟿豈-￿]`+pt+"*|[\uD800-\uDBFF][\uDC00-\uDFFF]"+pt+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|"+_1+"|\\\\[^\uD800-\uDFFF])";class mr{constructor(e,r){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=r,this.tokenRegex=new RegExp(j1,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,r){this.catcodes[e]=r}lex(){var e=this.input,r=this.tokenRegex.lastIndex;if(r===e.length)return new g0("EOF",new p0(this,r,r));var a=this.tokenRegex.exec(e);if(a===null||a.index!==r)throw new M("Unexpected character: '"+e[r]+"'",new g0(e[r],new p0(this,r,r+1)));var n=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[n]===14){var s=e.indexOf(`
`,this.tokenRegex.lastIndex);return s===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=s+1,this.lex()}return new g0(n,new p0(this,r,this.tokenRegex.lastIndex))}}class $1{constructor(e,r){e===void 0&&(e={}),r===void 0&&(r={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=r,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new M("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var r in e)e.hasOwnProperty(r)&&(e[r]==null?delete this.current[r]:this.current[r]=e[r])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,r,a){if(a===void 0&&(a=!1),a){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=r)}else{var s=this.undefStack[this.undefStack.length-1];s&&!s.hasOwnProperty(e)&&(s[e]=this.current[e])}r==null?delete this.current[e]:this.current[e]=r}}var Z1=Pr;m("\\noexpand",function(t){var e=t.popToken();return t.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}}),m("\\expandafter",function(t){var e=t.popToken();return t.expandOnce(!0),{tokens:[e],numArgs:0}}),m("\\@firstoftwo",function(t){return{tokens:t.consumeArgs(2)[0],numArgs:0}}),m("\\@secondoftwo",function(t){return{tokens:t.consumeArgs(2)[1],numArgs:0}}),m("\\@ifnextchar",function(t){var e=t.consumeArgs(3);t.consumeSpaces();var r=t.future();return e[0].length===1&&e[0][0].text===r.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),m("\\TextOrMath",function(t){var e=t.consumeArgs(2);return t.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var cr={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(t){var e,r=t.popToken(),a="";if(r.text==="'")e=8,r=t.popToken();else if(r.text==='"')e=16,r=t.popToken();else if(r.text==="`")if((r=t.popToken()).text[0]==="\\")a=r.text.charCodeAt(1);else{if(r.text==="EOF")throw new M("\\char` missing argument");a=r.text.charCodeAt(0)}else e=10;if(e){if((a=cr[r.text])==null||a>=e)throw new M("Invalid base-"+e+" digit "+r.text);for(var n;(n=cr[t.future().text])!=null&&n<e;)a*=e,a+=n,t.popToken()}return"\\@char{"+a+"}"});var Qe=(t,e,r,a)=>{var n=t.consumeArg().tokens;if(n.length!==1)throw new M("\\newcommand's first argument must be a macro name");var s=n[0].text,h=t.isDefined(s);if(h&&!e)throw new M("\\newcommand{"+s+"} attempting to redefine "+s+"; use \\renewcommand");if(!h&&!r)throw new M("\\renewcommand{"+s+"} when command "+s+" does not yet exist; use \\newcommand");var c=0;if((n=t.consumeArg().tokens).length===1&&n[0].text==="["){for(var p="",g=t.expandNextToken();g.text!=="]"&&g.text!=="EOF";)p+=g.text,g=t.expandNextToken();if(!p.match(/^\s*[0-9]+\s*$/))throw new M("Invalid number of arguments: "+p);c=parseInt(p),n=t.consumeArg().tokens}return h&&a||t.macros.set(s,{tokens:n,numArgs:c}),""};m("\\newcommand",t=>Qe(t,!1,!0,!1)),m("\\renewcommand",t=>Qe(t,!0,!1,!1)),m("\\providecommand",t=>Qe(t,!0,!0,!0)),m("\\message",t=>{var e=t.consumeArgs(1)[0];return console.log(e.reverse().map(r=>r.text).join("")),""}),m("\\errmessage",t=>{var e=t.consumeArgs(1)[0];return console.error(e.reverse().map(r=>r.text).join("")),""}),m("\\show",t=>{var e=t.popToken(),r=e.text;return console.log(e,t.macros.get(r),F0[r],$.math[r],$.text[r]),""}),m("\\bgroup","{"),m("\\egroup","}"),m("~","\\nobreakspace"),m("\\lq","`"),m("\\rq","'"),m("\\aa","\\r a"),m("\\AA","\\r A"),m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`©}"),m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`®}"),m("ℬ","\\mathscr{B}"),m("ℰ","\\mathscr{E}"),m("ℱ","\\mathscr{F}"),m("ℋ","\\mathscr{H}"),m("ℐ","\\mathscr{I}"),m("ℒ","\\mathscr{L}"),m("ℳ","\\mathscr{M}"),m("ℛ","\\mathscr{R}"),m("ℭ","\\mathfrak{C}"),m("ℌ","\\mathfrak{H}"),m("ℨ","\\mathfrak{Z}"),m("\\Bbbk","\\Bbb{k}"),m("·","\\cdotp"),m("\\llap","\\mathllap{\\textrm{#1}}"),m("\\rlap","\\mathrlap{\\textrm{#1}}"),m("\\clap","\\mathclap{\\textrm{#1}}"),m("\\mathstrut","\\vphantom{(}"),m("\\underbar","\\underline{\\text{#1}}"),m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}"),m("\\ne","\\neq"),m("≠","\\neq"),m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}"),m("∉","\\notin"),m("≘","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}"),m("≙","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}"),m("≚","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}"),m("≛","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}"),m("≝","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}"),m("≞","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}"),m("≟","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}"),m("⟂","\\perp"),m("‼","\\mathclose{!\\mkern-0.8mu!}"),m("∌","\\notni"),m("⌜","\\ulcorner"),m("⌝","\\urcorner"),m("⌞","\\llcorner"),m("⌟","\\lrcorner"),m("©","\\copyright"),m("®","\\textregistered"),m("️","\\textregistered"),m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),m("\\vdots","{\\varvdots\\rule{0pt}{15pt}}"),m("⋮","\\vdots"),m("\\varGamma","\\mathit{\\Gamma}"),m("\\varDelta","\\mathit{\\Delta}"),m("\\varTheta","\\mathit{\\Theta}"),m("\\varLambda","\\mathit{\\Lambda}"),m("\\varXi","\\mathit{\\Xi}"),m("\\varPi","\\mathit{\\Pi}"),m("\\varSigma","\\mathit{\\Sigma}"),m("\\varUpsilon","\\mathit{\\Upsilon}"),m("\\varPhi","\\mathit{\\Phi}"),m("\\varPsi","\\mathit{\\Psi}"),m("\\varOmega","\\mathit{\\Omega}"),m("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),m("\\boxed","\\fbox{$\\displaystyle{#1}$}"),m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),m("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;"),m("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}"),m("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");var pr={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(t){var e="\\dotso",r=t.expandAfterFuture().text;return r in pr?e=pr[r]:(r.slice(0,4)==="\\not"||r in $.math&&R.contains(["bin","rel"],$.math[r].group))&&(e="\\dotsb"),e});var et={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(t){return t.future().text in et?"\\ldots\\,":"\\ldots"}),m("\\dotsc",function(t){var e=t.future().text;return e in et&&e!==","?"\\ldots\\,":"\\ldots"}),m("\\cdots",function(t){return t.future().text in et?"\\@cdots\\,":"\\@cdots"}),m("\\dotsb","\\cdots"),m("\\dotsm","\\cdots"),m("\\dotsi","\\!\\cdots"),m("\\dotsx","\\ldots\\,"),m("\\DOTSI","\\relax"),m("\\DOTSB","\\relax"),m("\\DOTSX","\\relax"),m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),m("\\,","\\tmspace+{3mu}{.1667em}"),m("\\thinspace","\\,"),m("\\>","\\mskip{4mu}"),m("\\:","\\tmspace+{4mu}{.2222em}"),m("\\medspace","\\:"),m("\\;","\\tmspace+{5mu}{.2777em}"),m("\\thickspace","\\;"),m("\\!","\\tmspace-{3mu}{.1667em}"),m("\\negthinspace","\\!"),m("\\negmedspace","\\tmspace-{4mu}{.2222em}"),m("\\negthickspace","\\tmspace-{5mu}{.277em}"),m("\\enspace","\\kern.5em "),m("\\enskip","\\hskip.5em\\relax"),m("\\quad","\\hskip1em\\relax"),m("\\qquad","\\hskip2em\\relax"),m("\\tag","\\@ifstar\\tag@literal\\tag@paren"),m("\\tag@paren","\\tag@literal{({#1})}"),m("\\tag@literal",t=>{if(t.macros.get("\\df@tag"))throw new M("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),m("\\newline","\\\\\\relax"),m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var ur=A(S0["Main-Regular"][84][1]-.7*S0["Main-Regular"][65][1]);m("\\LaTeX","\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+ur+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}"),m("\\KaTeX","\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+ur+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}"),m("\\hspace","\\@ifstar\\@hspacer\\@hspace"),m("\\@hspace","\\hskip #1\\relax"),m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),m("\\ordinarycolon",":"),m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),m("∷","\\dblcolon"),m("∹","\\eqcolon"),m("≔","\\coloneqq"),m("≕","\\eqqcolon"),m("⩴","\\Coloneqq"),m("\\ratio","\\vcentcolon"),m("\\coloncolon","\\dblcolon"),m("\\colonequals","\\coloneqq"),m("\\coloncolonequals","\\Coloneqq"),m("\\equalscolon","\\eqqcolon"),m("\\equalscoloncolon","\\Eqqcolon"),m("\\colonminus","\\coloneq"),m("\\coloncolonminus","\\Coloneq"),m("\\minuscolon","\\eqcolon"),m("\\minuscoloncolon","\\Eqcolon"),m("\\coloncolonapprox","\\Colonapprox"),m("\\coloncolonsim","\\Colonsim"),m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}"),m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{≩}"),m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{≨}"),m("\\ngeqq","\\html@mathml{\\@ngeqq}{≱}"),m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{≱}"),m("\\nleqq","\\html@mathml{\\@nleqq}{≰}"),m("\\nleqslant","\\html@mathml{\\@nleqslant}{≰}"),m("\\nshortmid","\\html@mathml{\\@nshortmid}{∤}"),m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{∦}"),m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{⊈}"),m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{⊉}"),m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{⊊}"),m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{⫋}"),m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{⊋}"),m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{⫌}"),m("\\imath","\\html@mathml{\\@imath}{ı}"),m("\\jmath","\\html@mathml{\\@jmath}{ȷ}"),m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}"),m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}"),m("⟦","\\llbracket"),m("⟧","\\rrbracket"),m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}"),m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}"),m("⦃","\\lBrace"),m("⦄","\\rBrace"),m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}"),m("⦵","\\minuso"),m("\\darr","\\downarrow"),m("\\dArr","\\Downarrow"),m("\\Darr","\\Downarrow"),m("\\lang","\\langle"),m("\\rang","\\rangle"),m("\\uarr","\\uparrow"),m("\\uArr","\\Uparrow"),m("\\Uarr","\\Uparrow"),m("\\N","\\mathbb{N}"),m("\\R","\\mathbb{R}"),m("\\Z","\\mathbb{Z}"),m("\\alef","\\aleph"),m("\\alefsym","\\aleph"),m("\\Alpha","\\mathrm{A}"),m("\\Beta","\\mathrm{B}"),m("\\bull","\\bullet"),m("\\Chi","\\mathrm{X}"),m("\\clubs","\\clubsuit"),m("\\cnums","\\mathbb{C}"),m("\\Complex","\\mathbb{C}"),m("\\Dagger","\\ddagger"),m("\\diamonds","\\diamondsuit"),m("\\empty","\\emptyset"),m("\\Epsilon","\\mathrm{E}"),m("\\Eta","\\mathrm{H}"),m("\\exist","\\exists"),m("\\harr","\\leftrightarrow"),m("\\hArr","\\Leftrightarrow"),m("\\Harr","\\Leftrightarrow"),m("\\hearts","\\heartsuit"),m("\\image","\\Im"),m("\\infin","\\infty"),m("\\Iota","\\mathrm{I}"),m("\\isin","\\in"),m("\\Kappa","\\mathrm{K}"),m("\\larr","\\leftarrow"),m("\\lArr","\\Leftarrow"),m("\\Larr","\\Leftarrow"),m("\\lrarr","\\leftrightarrow"),m("\\lrArr","\\Leftrightarrow"),m("\\Lrarr","\\Leftrightarrow"),m("\\Mu","\\mathrm{M}"),m("\\natnums","\\mathbb{N}"),m("\\Nu","\\mathrm{N}"),m("\\Omicron","\\mathrm{O}"),m("\\plusmn","\\pm"),m("\\rarr","\\rightarrow"),m("\\rArr","\\Rightarrow"),m("\\Rarr","\\Rightarrow"),m("\\real","\\Re"),m("\\reals","\\mathbb{R}"),m("\\Reals","\\mathbb{R}"),m("\\Rho","\\mathrm{P}"),m("\\sdot","\\cdot"),m("\\sect","\\S"),m("\\spades","\\spadesuit"),m("\\sub","\\subset"),m("\\sube","\\subseteq"),m("\\supe","\\supseteq"),m("\\Tau","\\mathrm{T}"),m("\\thetasym","\\vartheta"),m("\\weierp","\\wp"),m("\\Zeta","\\mathrm{Z}"),m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),m("\\bra","\\mathinner{\\langle{#1}|}"),m("\\ket","\\mathinner{|{#1}\\rangle}"),m("\\braket","\\mathinner{\\langle{#1}\\rangle}"),m("\\Bra","\\left\\langle#1\\right|"),m("\\Ket","\\left|#1\\right\\rangle");var dr=t=>e=>{var r=e.consumeArg().tokens,a=e.consumeArg().tokens,n=e.consumeArg().tokens,s=e.consumeArg().tokens,h=e.macros.get("|"),c=e.macros.get("\\|");e.macros.beginGroup();var p=x=>v=>{t&&(v.macros.set("|",h),n.length&&v.macros.set("\\|",c));var w=x;return!x&&n.length&&v.future().text==="|"&&(v.popToken(),w=!0),{tokens:w?n:a,numArgs:0}};e.macros.set("|",p(!1)),n.length&&e.macros.set("\\|",p(!0));var g=e.consumeArg().tokens,b=e.expandTokens([...s,...g,...r]);return e.macros.endGroup(),{tokens:b.reverse(),numArgs:0}};m("\\bra@ket",dr(!1)),m("\\bra@set",dr(!0)),m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),m("\\angln","{\\angl n}"),m("\\blue","\\textcolor{##6495ed}{#1}"),m("\\orange","\\textcolor{##ffa500}{#1}"),m("\\pink","\\textcolor{##ff00af}{#1}"),m("\\red","\\textcolor{##df0030}{#1}"),m("\\green","\\textcolor{##28ae7b}{#1}"),m("\\gray","\\textcolor{gray}{#1}"),m("\\purple","\\textcolor{##9d38bd}{#1}"),m("\\blueA","\\textcolor{##ccfaff}{#1}"),m("\\blueB","\\textcolor{##80f6ff}{#1}"),m("\\blueC","\\textcolor{##63d9ea}{#1}"),m("\\blueD","\\textcolor{##11accd}{#1}"),m("\\blueE","\\textcolor{##0c7f99}{#1}"),m("\\tealA","\\textcolor{##94fff5}{#1}"),m("\\tealB","\\textcolor{##26edd5}{#1}"),m("\\tealC","\\textcolor{##01d1c1}{#1}"),m("\\tealD","\\textcolor{##01a995}{#1}"),m("\\tealE","\\textcolor{##208170}{#1}"),m("\\greenA","\\textcolor{##b6ffb0}{#1}"),m("\\greenB","\\textcolor{##8af281}{#1}"),m("\\greenC","\\textcolor{##74cf70}{#1}"),m("\\greenD","\\textcolor{##1fab54}{#1}"),m("\\greenE","\\textcolor{##0d923f}{#1}"),m("\\goldA","\\textcolor{##ffd0a9}{#1}"),m("\\goldB","\\textcolor{##ffbb71}{#1}"),m("\\goldC","\\textcolor{##ff9c39}{#1}"),m("\\goldD","\\textcolor{##e07d10}{#1}"),m("\\goldE","\\textcolor{##a75a05}{#1}"),m("\\redA","\\textcolor{##fca9a9}{#1}"),m("\\redB","\\textcolor{##ff8482}{#1}"),m("\\redC","\\textcolor{##f9685d}{#1}"),m("\\redD","\\textcolor{##e84d39}{#1}"),m("\\redE","\\textcolor{##bc2612}{#1}"),m("\\maroonA","\\textcolor{##ffbde0}{#1}"),m("\\maroonB","\\textcolor{##ff92c6}{#1}"),m("\\maroonC","\\textcolor{##ed5fa6}{#1}"),m("\\maroonD","\\textcolor{##ca337c}{#1}"),m("\\maroonE","\\textcolor{##9e034e}{#1}"),m("\\purpleA","\\textcolor{##ddd7ff}{#1}"),m("\\purpleB","\\textcolor{##c6b9fc}{#1}"),m("\\purpleC","\\textcolor{##aa87ff}{#1}"),m("\\purpleD","\\textcolor{##7854ab}{#1}"),m("\\purpleE","\\textcolor{##543b78}{#1}"),m("\\mintA","\\textcolor{##f5f9e8}{#1}"),m("\\mintB","\\textcolor{##edf2df}{#1}"),m("\\mintC","\\textcolor{##e0e5cc}{#1}"),m("\\grayA","\\textcolor{##f6f7f7}{#1}"),m("\\grayB","\\textcolor{##f0f1f2}{#1}"),m("\\grayC","\\textcolor{##e3e5e6}{#1}"),m("\\grayD","\\textcolor{##d6d8da}{#1}"),m("\\grayE","\\textcolor{##babec2}{#1}"),m("\\grayF","\\textcolor{##888d93}{#1}"),m("\\grayG","\\textcolor{##626569}{#1}"),m("\\grayH","\\textcolor{##3b3e40}{#1}"),m("\\grayI","\\textcolor{##21242c}{#1}"),m("\\kaBlue","\\textcolor{##314453}{#1}"),m("\\kaGreen","\\textcolor{##71B307}{#1}");var jr={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class K1{constructor(e,r,a){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=r,this.expansionCount=0,this.feed(e),this.macros=new $1(Z1,r.macros),this.mode=a,this.stack=[]}feed(e){this.lexer=new mr(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var r,a,n;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;r=this.popToken(),{tokens:n,end:a}=this.consumeArg(["]"])}else({tokens:n,start:r,end:a}=this.consumeArg());return this.pushToken(new g0("EOF",a.loc)),this.pushTokens(n),r.range(a,"")}consumeSpaces(){for(;this.future().text===" ";)this.stack.pop()}consumeArg(e){var r=[],a=e&&e.length>0;a||this.consumeSpaces();var n,s=this.future(),h=0,c=0;do{if(n=this.popToken(),r.push(n),n.text==="{")++h;else if(n.text==="}"){if(--h===-1)throw new M("Extra }",n)}else if(n.text==="EOF")throw new M("Unexpected end of input in a macro argument, expected '"+(e&&a?e[c]:"}")+"'",n);if(e&&a)if((h===0||h===1&&e[c]==="{")&&n.text===e[c]){if(++c===e.length){r.splice(-c,c);break}}else c=0}while(h!==0||a);return s.text==="{"&&r[r.length-1].text==="}"&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:s,end:n}}consumeArgs(e,r){if(r){if(r.length!==e+1)throw new M("The length of delimiters doesn't match the number of args!");for(var a=r[0],n=0;n<a.length;n++){var s=this.popToken();if(a[n]!==s.text)throw new M("Use of the macro doesn't match its definition",s)}}for(var h=[],c=0;c<e;c++)h.push(this.consumeArg(r&&r[c+1]).tokens);return h}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new M("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var r=this.popToken(),a=r.text,n=r.noexpand?null:this._getExpansion(a);if(n==null||e&&n.unexpandable){if(e&&n==null&&a[0]==="\\"&&!this.isDefined(a))throw new M("Undefined control sequence: "+a);return this.pushToken(r),!1}this.countExpansion(1);var s=n.tokens,h=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs)for(var c=(s=s.slice()).length-1;c>=0;--c){var p=s[c];if(p.text==="#"){if(c===0)throw new M("Incomplete placeholder at end of macro body",p);if((p=s[--c]).text==="#")s.splice(c+1,1);else{if(!/^[1-9]$/.test(p.text))throw new M("Not a valid argument number",p);s.splice(c,2,...h[+p.text-1])}}}return this.pushTokens(s),s.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new g0(e)]):void 0}expandTokens(e){var r=[],a=this.stack.length;for(this.pushTokens(e);this.stack.length>a;)if(this.expandOnce(!0)===!1){var n=this.stack.pop();n.treatAsRelax&&(n.noexpand=!1,n.treatAsRelax=!1),r.push(n)}return this.countExpansion(r.length),r}expandMacroAsText(e){var r=this.expandMacro(e);return r&&r.map(a=>a.text).join("")}_getExpansion(e){var r=this.macros.get(e);if(r==null)return r;if(e.length===1){var a=this.lexer.catcodes[e];if(a!=null&&a!==13)return}var n=typeof r=="function"?r(this):r;if(typeof n=="string"){var s=0;if(n.indexOf("#")!==-1)for(var h=n.replace(/##/g,"");h.indexOf("#"+(s+1))!==-1;)++s;for(var c=new mr(n,this.settings),p=[],g=c.lex();g.text!=="EOF";)p.push(g),g=c.lex();return p.reverse(),{tokens:p,numArgs:s}}return n}isDefined(e){return this.macros.has(e)||F0.hasOwnProperty(e)||$.math.hasOwnProperty(e)||$.text.hasOwnProperty(e)||jr.hasOwnProperty(e)}isExpandable(e){var r=this.macros.get(e);return r!=null?typeof r=="string"||typeof r=="function"||!r.unexpandable:F0.hasOwnProperty(e)&&!F0[e].primitive}}var gr=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,ye=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9","ₐ":"a","ₑ":"e","ₕ":"h","ᵢ":"i","ⱼ":"j","ₖ":"k","ₗ":"l","ₘ":"m","ₙ":"n","ₒ":"o","ₚ":"p","ᵣ":"r","ₛ":"s","ₜ":"t","ᵤ":"u","ᵥ":"v","ₓ":"x","ᵦ":"β","ᵧ":"γ","ᵨ":"ρ","ᵩ":"ϕ","ᵪ":"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","¹":"1","²":"2","³":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9","ᴬ":"A","ᴮ":"B","ᴰ":"D","ᴱ":"E","ᴳ":"G","ᴴ":"H","ᴵ":"I","ᴶ":"J","ᴷ":"K","ᴸ":"L","ᴹ":"M","ᴺ":"N","ᴼ":"O","ᴾ":"P","ᴿ":"R","ᵀ":"T","ᵁ":"U","ⱽ":"V","ᵂ":"W","ᵃ":"a","ᵇ":"b","ᶜ":"c","ᵈ":"d","ᵉ":"e","ᶠ":"f","ᵍ":"g",ʰ:"h","ⁱ":"i",ʲ:"j","ᵏ":"k",ˡ:"l","ᵐ":"m",ⁿ:"n","ᵒ":"o","ᵖ":"p",ʳ:"r",ˢ:"s","ᵗ":"t","ᵘ":"u","ᵛ":"v",ʷ:"w",ˣ:"x",ʸ:"y","ᶻ":"z","ᵝ":"β","ᵞ":"γ","ᵟ":"δ","ᵠ":"ϕ","ᵡ":"χ","ᶿ":"θ"}),tt={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},fr={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class Ne{constructor(e,r){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new K1(e,r,this.mode),this.settings=r,this.leftrightDepth=0}expect(e,r){if(r===void 0&&(r=!0),this.fetch().text!==e)throw new M("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());r&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var r=this.nextToken;this.consume(),this.gullet.pushToken(new g0("}")),this.gullet.pushTokens(e);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=r,a}parseExpression(e,r){for(var a=[];;){this.mode==="math"&&this.consumeSpaces();var n=this.fetch();if(Ne.endOfExpression.indexOf(n.text)!==-1||r&&n.text===r||e&&F0[n.text]&&F0[n.text].infix)break;var s=this.parseAtom(r);if(!s)break;s.type!=="internal"&&a.push(s)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)}handleInfixNodes(e){for(var r,a=-1,n=0;n<e.length;n++)if(e[n].type==="infix"){if(a!==-1)throw new M("only one infix operator per group",e[n].token);a=n,r=e[n].replaceWith}if(a!==-1&&r){var s,h,c=e.slice(0,a),p=e.slice(a+1);return s=c.length===1&&c[0].type==="ordgroup"?c[0]:{type:"ordgroup",mode:this.mode,body:c},h=p.length===1&&p[0].type==="ordgroup"?p[0]:{type:"ordgroup",mode:this.mode,body:p},[r==="\\\\abovefrac"?this.callFunction(r,[s,e[a],h],[]):this.callFunction(r,[s,h],[])]}return e}handleSupSubscript(e){var r=this.fetch(),a=r.text;this.consume(),this.consumeSpaces();var n=this.parseGroup(e);if(!n)throw new M("Expected group after '"+a+"'",r);return n}formatUnsupportedCmd(e){for(var r=[],a=0;a<e.length;a++)r.push({type:"textord",mode:"text",text:e[a]});var n={type:"text",mode:this.mode,body:r};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]}}parseAtom(e){var r,a,n=this.parseGroup("atom",e);if(this.mode==="text")return n;for(;;){this.consumeSpaces();var s=this.fetch();if(s.text==="\\limits"||s.text==="\\nolimits"){if(n&&n.type==="op"){var h=s.text==="\\limits";n.limits=h,n.alwaysHandleSupSub=!0}else{if(!n||n.type!=="operatorname")throw new M("Limit controls must follow a math operator",s);n.alwaysHandleSupSub&&(n.limits=s.text==="\\limits")}this.consume()}else if(s.text==="^"){if(r)throw new M("Double superscript",s);r=this.handleSupSubscript("superscript")}else if(s.text==="_"){if(a)throw new M("Double subscript",s);a=this.handleSupSubscript("subscript")}else if(s.text==="'"){if(r)throw new M("Double superscript",s);var c={type:"textord",mode:this.mode,text:"\\prime"},p=[c];for(this.consume();this.fetch().text==="'";)p.push(c),this.consume();this.fetch().text==="^"&&p.push(this.handleSupSubscript("superscript")),r={type:"ordgroup",mode:this.mode,body:p}}else{if(!ye[s.text])break;var g=gr.test(s.text),b=[];for(b.push(new g0(ye[s.text])),this.consume();;){var x=this.fetch().text;if(!ye[x]||gr.test(x)!==g)break;b.unshift(new g0(ye[x])),this.consume()}var v=this.subparse(b);g?a={type:"ordgroup",mode:"math",body:v}:r={type:"ordgroup",mode:"math",body:v}}}return r||a?{type:"supsub",mode:this.mode,base:n,sup:r,sub:a}:n}parseFunction(e,r){var a=this.fetch(),n=a.text,s=F0[n];if(!s)return null;if(this.consume(),r&&r!=="atom"&&!s.allowedInArgument)throw new M("Got function '"+n+"' with no arguments"+(r?" as "+r:""),a);if(this.mode==="text"&&!s.allowedInText)throw new M("Can't use function '"+n+"' in text mode",a);if(this.mode==="math"&&s.allowedInMath===!1)throw new M("Can't use function '"+n+"' in math mode",a);var{args:h,optArgs:c}=this.parseArguments(n,s);return this.callFunction(n,h,c,a,e)}callFunction(e,r,a,n,s){var h={funcName:e,parser:this,token:n,breakOnTokenText:s},c=F0[e];if(c&&c.handler)return c.handler(h,r,a);throw new M("No function handler for "+e)}parseArguments(e,r){var a=r.numArgs+r.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};for(var n=[],s=[],h=0;h<a;h++){var c=r.argTypes&&r.argTypes[h],p=h<r.numOptionalArgs;(r.primitive&&c==null||r.type==="sqrt"&&h===1&&s[0]==null)&&(c="primitive");var g=this.parseGroupOfType("argument to '"+e+"'",c,p);if(p)s.push(g);else{if(g==null)throw new M("Null argument, please report this as a bug");n.push(g)}}return{args:n,optArgs:s}}parseGroupOfType(e,r,a){switch(r){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,r);case"hbox":var n=this.parseArgumentGroup(a,"text");return n!=null?{type:"styling",mode:n.mode,body:[n],style:"text"}:null;case"raw":var s=this.parseStringGroup("raw",a);return s!=null?{type:"raw",mode:"text",string:s.text}:null;case"primitive":if(a)throw new M("A primitive argument cannot be optional");var h=this.parseGroup(e);if(h==null)throw new M("Expected group as "+e,this.fetch());return h;case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new M("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,r){var a=this.gullet.scanArgument(r);if(a==null)return null;for(var n,s="";(n=this.fetch()).text!=="EOF";)s+=n.text,this.consume();return this.consume(),a.text=s,a}parseRegexGroup(e,r){for(var a,n=this.fetch(),s=n,h="";(a=this.fetch()).text!=="EOF"&&e.test(h+a.text);)h+=(s=a).text,this.consume();if(h==="")throw new M("Invalid "+r+": '"+n.text+"'",n);return n.range(s,h)}parseColorGroup(e){var r=this.parseStringGroup("color",e);if(r==null)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(r.text);if(!a)throw new M("Invalid color: '"+r.text+"'",r);var n=a[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){var r,a=!1;if(this.gullet.consumeSpaces(),!(r=e||this.gullet.future().text==="{"?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||r.text.length!==0||(r.text="0pt",a=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(r.text);if(!n)throw new M("Invalid size: '"+r.text+"'",r);var s={number:+(n[1]+n[2]),unit:n[3]};if(!br(s))throw new M("Invalid unit: '"+s.unit+"'",r);return{type:"size",mode:this.mode,value:s,isBlank:a}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var r=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),r==null)return null;var a=r.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}}parseArgumentGroup(e,r){var a=this.gullet.scanArgument(e);if(a==null)return null;var n=this.mode;r&&this.switchMode(r),this.gullet.beginGroup();var s=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var h={type:"ordgroup",mode:this.mode,loc:a.loc,body:s};return r&&this.switchMode(n),h}parseGroup(e,r){var a,n=this.fetch(),s=n.text;if(s==="{"||s==="\\begingroup"){this.consume();var h=s==="{"?"}":"\\endgroup";this.gullet.beginGroup();var c=this.parseExpression(!1,h),p=this.fetch();this.expect(h),this.gullet.endGroup(),a={type:"ordgroup",mode:this.mode,loc:p0.range(n,p),body:c,semisimple:s==="\\begingroup"||void 0}}else if((a=this.parseFunction(r,e)||this.parseSymbol())==null&&s[0]==="\\"&&!jr.hasOwnProperty(s)){if(this.settings.throwOnError)throw new M("Undefined control sequence: "+s,n);a=this.formatUnsupportedCmd(s),this.consume()}return a}formLigatures(e){for(var r=e.length-1,a=0;a<r;++a){var n=e[a],s=n.text;s==="-"&&e[a+1].text==="-"&&(a+1<r&&e[a+2].text==="-"?(e.splice(a,3,{type:"textord",mode:"text",loc:p0.range(n,e[a+2]),text:"---"}),r-=2):(e.splice(a,2,{type:"textord",mode:"text",loc:p0.range(n,e[a+1]),text:"--"}),r-=1)),s!=="'"&&s!=="`"||e[a+1].text!==s||(e.splice(a,2,{type:"textord",mode:"text",loc:p0.range(n,e[a+1]),text:s+s}),r-=1)}}parseSymbol(){var e=this.fetch(),r=e.text;if(/^\\verb[^a-zA-Z]/.test(r)){this.consume();var a=r.slice(5),n=a.charAt(0)==="*";if(n&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new M(`\\verb assertion failed --
                    please report what input caused this bug`);return{type:"verb",mode:"text",body:a=a.slice(1,-1),star:n}}fr.hasOwnProperty(r[0])&&!$[this.mode][r[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+r[0]+'" used in math mode',e),r=fr[r[0]]+r.slice(1));var s,h=W1.exec(r);if(h&&((r=r.substring(0,h.index))==="i"?r="ı":r==="j"&&(r="ȷ")),$[this.mode][r]){this.settings.strict&&this.mode==="math"&&Mr.indexOf(r)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+r[0]+'" used in math mode',e);var c,p=$[this.mode][r].group,g=p0.range(e);if(f1.hasOwnProperty(p)){var b=p;c={type:"atom",mode:this.mode,family:b,loc:g,text:r}}else c={type:p,mode:this.mode,loc:g,text:r};s=c}else{if(!(r.charCodeAt(0)>=128))return null;this.settings.strict&&(vr(r.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+r[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+r[0]+'" ('+r.charCodeAt(0)+")",e)),s={type:"textord",mode:"text",loc:p0.range(e),text:r}}if(this.consume(),h)for(var x=0;x<h[0].length;x++){var v=h[0][x];if(!tt[v])throw new M("Unknown accent ' "+v+"'",e);var w=tt[v][this.mode]||tt[v].text;if(!w)throw new M("Accent "+v+" unsupported in "+this.mode+" mode",e);s={type:"accent",mode:this.mode,loc:p0.range(e),label:w,isStretchy:!1,isShifty:!0,base:s}}return s}}Ne.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var yt=function(t,e){if(!(typeof t=="string"||t instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new Ne(t,e);delete r.gullet.macros.current["\\df@tag"];var a=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!e.displayMode)throw new M("\\tag works only in display equations");a=[{type:"tag",mode:"text",body:a,tag:r.subparse([new g0("\\df@tag")])}]}return a},$r=function(t,e,r){e.textContent="";var a=xt(t,r).toNode();e.appendChild(a)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),$r=function(){throw new M("KaTeX doesn't work in quirks mode.")});var J1=function(t,e){return xt(t,e).toMarkup()},Q1=function(t,e){var r=new ut(e);return yt(t,r)},Zr=function(t,e,r){if(r.throwOnError||!(t instanceof M))throw t;var a=y.makeSpan(["katex-error"],[new f0(e)]);return a.setAttribute("title",t.toString()),a.setAttribute("style","color:"+r.errorColor),a},xt=function(t,e){var r=new ut(e);try{return function(a,n,s){var h,c=Br(s);if(s.output==="mathml")return Lt(a,n,c,s.displayMode,!0);if(s.output==="html"){var p=ot(a,c);h=y.makeSpan(["katex"],[p])}else{var g=Lt(a,n,c,s.displayMode,!1),b=ot(a,c);h=y.makeSpan(["katex"],[g,b])}return Cr(h,s)}(yt(t,r),t,r)}catch(a){return Zr(a,t,r)}},ea=function(t,e){var r=new ut(e);try{return function(a,n,s){var h=ot(a,Br(s)),c=y.makeSpan(["katex"],[h]);return Cr(c,s)}(yt(t,r),0,r)}catch(a){return Zr(a,t,r)}},ta="0.16.21",ra={Span:ae,Anchor:gt,SymbolNode:f0,SvgNode:H0,PathNode:Y0,LineNode:nt},aa={version:ta,render:$r,renderToString:J1,ParseError:M,SETTINGS_SCHEMA:xe,__parse:Q1,__renderToDomTree:xt,__renderToHTMLTree:ea,__setFontMetrics:m1,__defineSymbol:i,__defineFunction:B,__defineMacro:m,__domTree:ra};export{M as ParseError,xe as SETTINGS_SCHEMA,B as __defineFunction,m as __defineMacro,i as __defineSymbol,ra as __domTree,Q1 as __parse,xt as __renderToDomTree,ea as __renderToHTMLTree,m1 as __setFontMetrics,aa as default,$r as render,J1 as renderToString,ta as version};
