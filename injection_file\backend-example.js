/**
 * 简单的后端API示例 - Node.js + Express
 * 用于演示如何提供token管理API
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 配置区域 - 方便修改
const AUTH_PASSWORD = 'your_secret_password';  // 修改为你的认证密码
const DEFAULT_TENANT_URL = 'https://d5.api.augmentcode.com/';

// 模拟的token数据库
const tokenDatabase = [
    {
        id: 'token_001',
        accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        tenantURL: DEFAULT_TENANT_URL,
        description: 'Token 1 - 测试账号A',
        createdAt: new Date().toISOString()
    },
    {
        id: 'token_002', 
        accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwOTg3NjU0MzIxIiwibmFtZSI6IkphbmUgU21pdGgiLCJpYXQiOjE1MTYyMzkwMjJ9.f1nZwIiOiJKV1QiLCJhbGciOiJIUzI1NiJ9',
        tenantURL: DEFAULT_TENANT_URL,
        description: 'Token 2 - 测试账号B',
        createdAt: new Date().toISOString()
    },
    {
        id: 'token_003',
        accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMTIyMzM0NDU1IiwibmFtZSI6IkJvYiBKb2huc29uIiwiaWF0IjoxNTE2MjM5MDIyfQ.xyz789abc123def456ghi789jkl012mno345pqr678stu901vwx234',
        tenantURL: DEFAULT_TENANT_URL,
        description: 'Token 3 - 测试账号C',
        createdAt: new Date().toISOString()
    }
];

// 中间件
app.use(cors());
app.use(express.json());

// 认证中间件
function authenticate(req, res, next) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            error: 'Missing or invalid Authorization header',
            message: 'Expected: Authorization: Bearer <password>'
        });
    }
    
    const token = authHeader.substring(7); // 移除 "Bearer " 前缀
    
    if (token !== AUTH_PASSWORD) {
        return res.status(401).json({
            error: 'Invalid authentication token',
            message: 'Please check your password'
        });
    }
    
    next();
}

// 日志中间件
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// API路由

/**
 * 获取可用token列表
 * GET /api/tokens
 * Authorization: Bearer <password>
 */
app.get('/api/tokens', authenticate, (req, res) => {
    try {
        console.log('获取token列表请求');
        
        res.json({
            success: true,
            tokens: tokenDatabase.map(token => ({
                id: token.id,
                accessToken: token.accessToken,
                tenantURL: token.tenantURL,
                description: token.description,
                createdAt: token.createdAt
            })),
            count: tokenDatabase.length,
            timestamp: new Date().toISOString()
        });
        
        console.log(`返回 ${tokenDatabase.length} 个token`);
    } catch (error) {
        console.error('获取token列表失败:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * 获取特定token信息
 * GET /api/tokens/:id
 * Authorization: Bearer <password>
 */
app.get('/api/tokens/:id', authenticate, (req, res) => {
    try {
        const tokenId = req.params.id;
        const token = tokenDatabase.find(t => t.id === tokenId);
        
        if (!token) {
            return res.status(404).json({
                success: false,
                error: 'Token not found',
                message: `Token with id '${tokenId}' does not exist`
            });
        }
        
        res.json({
            success: true,
            token: {
                id: token.id,
                accessToken: token.accessToken,
                tenantURL: token.tenantURL,
                description: token.description,
                createdAt: token.createdAt
            }
        });
        
        console.log(`返回token信息: ${tokenId}`);
    } catch (error) {
        console.error('获取token信息失败:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
});

/**
 * 健康检查
 * GET /health
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        tokenCount: tokenDatabase.length
    });
});

/**
 * API信息
 * GET /api/info
 */
app.get('/api/info', (req, res) => {
    res.json({
        name: 'Simple Token Manager API',
        version: '1.0.0',
        description: '为VSCode Simple Token Manager提供token管理服务',
        endpoints: {
            'GET /api/tokens': '获取所有可用token',
            'GET /api/tokens/:id': '获取特定token信息',
            'GET /health': '健康检查',
            'GET /api/info': 'API信息'
        },
        authentication: 'Bearer Token',
        timestamp: new Date().toISOString()
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `路径 ${req.originalUrl} 不存在`,
        availableEndpoints: [
            'GET /api/tokens',
            'GET /api/tokens/:id', 
            'GET /health',
            'GET /api/info'
        ]
    });
});

// 错误处理
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log('='.repeat(50));
    console.log('🚀 Simple Token Manager API Server');
    console.log('='.repeat(50));
    console.log(`📡 服务器运行在: http://localhost:${PORT}`);
    console.log(`🔑 认证密码: ${AUTH_PASSWORD}`);
    console.log(`📊 可用token数量: ${tokenDatabase.length}`);
    console.log('');
    console.log('📋 可用端点:');
    console.log(`   GET  /api/tokens        - 获取token列表`);
    console.log(`   GET  /api/tokens/:id    - 获取特定token`);
    console.log(`   GET  /health           - 健康检查`);
    console.log(`   GET  /api/info         - API信息`);
    console.log('');
    console.log('🔧 测试命令:');
    console.log(`   curl -H "Authorization: Bearer ${AUTH_PASSWORD}" http://localhost:${PORT}/api/tokens`);
    console.log('='.repeat(50));
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 收到关闭信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭服务器...');
    process.exit(0);
});
