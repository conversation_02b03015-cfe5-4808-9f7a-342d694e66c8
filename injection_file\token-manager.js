/**
 * 简化版Token管理器 - 无感换号核心模块
 * 基于success case的核心逻辑，简化实现
 */

class SimpleTokenManager {
    // ========== 配置区域 - 方便修改 ==========
    static API_BASE_URL = 'http://localhost:3000';  // 修改为你的后端地址
    static AUTH_PASSWORD = 'your_secret_password';   // 修改为你的认证密码
    static DEFAULT_TENANT_URL = 'https://d5.api.augmentcode.com/';
    static SCOPES = ['augment_external_v1_2024'];    // 与success case保持一致
    // =====================================

    constructor() {
        this.context = null;
        this.logger = {
            info: (msg) => console.log(`[TokenManager] ${msg}`),
            error: (msg, err) => console.error(`[TokenManager] ${msg}`, err),
            warn: (msg) => console.warn(`[TokenManager] ${msg}`)
        };
    }

    async initialize(context) {
        this.context = context;
        this.logger.info('Token Manager initialized');
    }

    /**
     * 获取可用的token列表
     */
    async getAvailableTokens() {
        try {
            const response = await fetch(`${SimpleTokenManager.API_BASE_URL}/api/tokens`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                },
                timeout: 10000
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            this.logger.info(`获取到 ${data.tokens?.length || 0} 个可用token`);

            return {
                success: true,
                tokens: data.tokens || []
            };
        } catch (error) {
            this.logger.error('获取token列表失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 注入token到VSCode secrets - 核心功能
     */
    async injectToken(accessToken, tenantURL = null) {
        try {
            const sessionData = {
                accessToken: accessToken,
                tenantURL: tenantURL || SimpleTokenManager.DEFAULT_TENANT_URL,
                scopes: SimpleTokenManager.SCOPES
            };

            // 关键：使用相同的键名劫持原插件的存储
            await this.context.secrets.store('augment.sessions', JSON.stringify(sessionData));

            this.logger.info('Token注入成功');
            return {
                success: true,
                data: sessionData
            };
        } catch (error) {
            this.logger.error('Token注入失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取当前存储的token信息
     */
    async getCurrentToken() {
        try {
            const sessionData = await this.context.secrets.get('augment.sessions');
            if (sessionData) {
                const data = JSON.parse(sessionData);
                return {
                    success: true,
                    accessToken: data.accessToken,
                    tenantURL: data.tenantURL,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: '未找到token信息'
                };
            }
        } catch (error) {
            this.logger.error('获取当前token失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 一键更新token - 自动选择第一个可用token
     */
    async quickUpdateToken() {
        try {
            const tokensResult = await this.getAvailableTokens();
            if (!tokensResult.success || !tokensResult.tokens.length) {
                return {
                    success: false,
                    error: '没有可用的token'
                };
            }

            // 选择第一个token
            const selectedToken = tokensResult.tokens[0];
            const injectResult = await this.injectToken(
                selectedToken.accessToken,
                selectedToken.tenantURL
            );

            if (injectResult.success) {
                this.logger.info(`一键更新成功，使用token: ${selectedToken.id || 'unknown'}`);
                return {
                    success: true,
                    message: `一键更新成功！使用了第1个token`,
                    data: injectResult.data
                };
            } else {
                return injectResult;
            }
        } catch (error) {
            this.logger.error('一键更新失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    dispose() {
        this.logger.info('Token Manager disposed');
    }
}

module.exports = SimpleTokenManager;
