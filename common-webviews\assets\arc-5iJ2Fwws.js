import{L as dt,M as lt,N as f,O as Tt,P as b,Q as T,R as rt,S as ft,T as N,V as xt,W as X,X as o,Y as gt,Z as At,$ as Rt}from"./AugmentMessage-FtcicXdY.js";function Ot(i){return i.innerRadius}function Pt(i){return i.outerRadius}function Lt(i){return i.startAngle}function Mt(i){return i.endAngle}function Nt(i){return i&&i.padAngle}function E(i,V,k,j,O,P,_){var e=i-k,G=V-j,c=(_?P:-P)/X(e*e+G*G),n=c*G,A=-c*e,u=i+n,a=V+A,y=k+n,l=j+A,$=(u+y)/2,s=(a+l)/2,t=y-u,r=l-a,m=t*t+r*r,h=O-P,x=u*l-y*a,v=(r<0?-1:1)*X(Rt(0,h*h*m-x*x)),L=(x*r-t*v)/m,M=(-x*t-r*v)/m,q=(x*r+t*v)/m,C=(-x*t+r*v)/m,d=L-$,g=M-s,p=q-$,D=C-s;return d*d+g*g>p*p+D*D&&(L=q,M=C),{cx:L,cy:M,x01:-n,y01:-A,x11:L*(O/h-1),y11:M*(O/h-1)}}function jt(){var i=Ot,V=Pt,k=N(0),j=null,O=Lt,P=Mt,_=Nt,e=null,G=dt(c);function c(){var n,A,u=+i.apply(this,arguments),a=+V.apply(this,arguments),y=O.apply(this,arguments)-lt,l=P.apply(this,arguments)-lt,$=ft(l-y),s=l>y;if(e||(e=n=G()),a<u&&(A=a,a=u,u=A),a>f)if($>Tt-f)e.moveTo(a*b(y),a*T(y)),e.arc(0,0,a,y,l,!s),u>f&&(e.moveTo(u*b(l),u*T(l)),e.arc(0,0,u,l,y,s));else{var t,r,m=y,h=l,x=y,v=l,L=$,M=$,q=_.apply(this,arguments)/2,C=q>f&&(j?+j.apply(this,arguments):X(u*u+a*a)),d=rt(ft(a-u)/2,+k.apply(this,arguments)),g=d,p=d;if(C>f){var D=gt(C/u*T(q)),F=gt(C/a*T(q));(L-=2*D)>f?(x+=D*=s?1:-1,v-=D):(L=0,x=v=(y+l)/2),(M-=2*F)>f?(m+=F*=s?1:-1,h-=F):(M=0,m=h=(y+l)/2)}var U=a*b(m),W=a*T(m),J=u*b(v),K=u*T(v);if(d>f){var R,Y=a*b(h),Z=a*T(h),w=u*b(x),z=u*T(x);if($<xt)if(R=function(tt,nt,pt,mt,at,ct,ht,vt){var st=pt-tt,it=mt-nt,ot=ht-at,yt=vt-ct,H=yt*st-ot*it;if(!(H*H<f))return[tt+(H=(ot*(nt-ct)-yt*(tt-at))/H)*st,nt+H*it]}(U,W,w,z,Y,Z,J,K)){var B=U-R[0],I=W-R[1],S=Y-R[0],Q=Z-R[1],et=1/T(At((B*S+I*Q)/(X(B*B+I*I)*X(S*S+Q*Q)))/2),ut=X(R[0]*R[0]+R[1]*R[1]);g=rt(d,(u-ut)/(et-1)),p=rt(d,(a-ut)/(et+1))}else g=p=0}M>f?p>f?(t=E(w,z,U,W,a,p,s),r=E(Y,Z,J,K,a,p,s),e.moveTo(t.cx+t.x01,t.cy+t.y01),p<d?e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,a,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),!s),e.arc(r.cx,r.cy,p,o(r.y11,r.x11),o(r.y01,r.x01),!s))):(e.moveTo(U,W),e.arc(0,0,a,m,h,!s)):e.moveTo(U,W),u>f&&L>f?g>f?(t=E(J,K,Y,Z,u,-g,s),r=E(U,W,w,z,u,-g,s),e.lineTo(t.cx+t.x01,t.cy+t.y01),g<d?e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,u,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),s),e.arc(r.cx,r.cy,g,o(r.y11,r.x11),o(r.y01,r.x01),!s))):e.arc(0,0,u,v,x,s):e.lineTo(J,K)}else e.moveTo(0,0);if(e.closePath(),n)return e=null,n+""||null}return c.centroid=function(){var n=(+i.apply(this,arguments)+ +V.apply(this,arguments))/2,A=(+O.apply(this,arguments)+ +P.apply(this,arguments))/2-xt/2;return[b(A)*n,T(A)*n]},c.innerRadius=function(n){return arguments.length?(i=typeof n=="function"?n:N(+n),c):i},c.outerRadius=function(n){return arguments.length?(V=typeof n=="function"?n:N(+n),c):V},c.cornerRadius=function(n){return arguments.length?(k=typeof n=="function"?n:N(+n),c):k},c.padRadius=function(n){return arguments.length?(j=n==null?null:typeof n=="function"?n:N(+n),c):j},c.startAngle=function(n){return arguments.length?(O=typeof n=="function"?n:N(+n),c):O},c.endAngle=function(n){return arguments.length?(P=typeof n=="function"?n:N(+n),c):P},c.padAngle=function(n){return arguments.length?(_=typeof n=="function"?n:N(+n),c):_},c.context=function(n){return arguments.length?(e=n??null,c):e},c}export{jt as d};
