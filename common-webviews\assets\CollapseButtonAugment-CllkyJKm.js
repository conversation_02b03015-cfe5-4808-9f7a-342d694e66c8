import{i as _e,aB as ze,aC as Ce,c as Le,p as Se,n as Fe,U as Ee,l as E,f as W,a as Z,t as h,b as z,v as He,u as Ie,z as Ae,A as pe,C as I,w as ce,aD as Te,X as F,$ as Be,D as fe,J as G,N as O,H as J,Q as ie,a3 as re,a4 as V,ab as B,_ as X,m as de,S as Me,R as Y,P as Ue,I as me,a2 as he,M as ue,K as M,Y as q,G as ge,L as De,a7 as Pe,F as Ke}from"./SpinnerAugment-BY2Lraps.js";import{F as U}from"./focusTrapStack-CAuiPHBF.js";import{h as j,a as Ne,I as Oe}from"./IconButtonAugment-B8y0FMb_.js";import{b as Ge}from"./CardAugment-BaFOe6RO.js";function ns(s,e,o){var t,l=s,m=Ee,c=_e()?ze:Ce;Le(()=>{c(m,m=e())&&(t&&Se(t),t=Fe(()=>o(l)))})}function ve(s){return[...s.querySelectorAll("*")].filter(e=>e.tabIndex>=0)}const as=(s,e={})=>{let{enabled:o=!0,initialFocus:t=null,restoreFocusOnClose:l=!0}=e,m=null,c=null,d=null;function g(){const i=ve(s);return[i[0]||s,i[i.length-1]||s]}function w(){if(!o)return;U.add(s),m=document.activeElement;const i=t||g()[0];i&&i.focus();const r=u=>{if(u.key!=="Tab"||!U.isActive(s))return;const n=document.activeElement;if(!s.contains(n))return;const[v,p]=g();u.shiftKey?n===v&&(u.preventDefault(),p.focus()):n===p&&(u.preventDefault(),v.focus())},x=u=>{if(!U.isActive(s))return;const n=u.target;if(s===n)return void(c==null?void 0:c.focus());if(s.contains(n))return void(c=n);if(ve(s).length===0)return;const[v]=g(),p=c||v;n!==p&&(p===s&&s.tabIndex<0||(document.body.contains(n)&&(c=p,p.focus()),n&&n!==document.body||(c=p,p.focus())))};document.addEventListener("keydown",r),document.addEventListener("focusin",x),d=()=>{document.removeEventListener("keydown",r),document.removeEventListener("focusin",x)}}function y(){U.remove(s),d&&(d(),d=null),l&&U.isEmpty()&&m&&typeof m.focus=="function"&&m.focus()}return o&&w(),{update(i={}){const r=o;o=i.enabled??o,t=i.initialFocus??t,l=i.restoreFocusOnClose??l,!r&&o?w():r&&!o&&y()},destroy(){y()}}};var Qe=W("<svg><!></svg>");function ls(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=Qe();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...o}));var l=h(t);j(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',!0),z(s,t)}var Re=W("<svg><!></svg>");function Xe(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=Re();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...o}));var l=h(t);j(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',!0),z(s,t)}const ye=Symbol("collapsible");function Ye(){return Ie(ye)}var qe=G('<footer class="c-collapsible__footer svelte-zpen55"><!></footer>'),Je=G("<div><!></div> <!>",1),Ve=G('<div><header><div data-collapsible-header="button" tabindex="0"><!></div></header> <div><div class="c-collapsible__content-inner svelte-zpen55"><!></div></div></div>');function cs(s,e){const o=Ae(e),t=E(e,["children","$$slots","$$events","$$legacy"]),l=E(t,["toggle","collapsed","stickyHeader","expandable","isHeaderStuck","toggleHeader","stickyHeaderTop"]);pe(e,!1);const[m,c]=he(),d=()=>V(v,"$collapsedStore",m),g=()=>V($,"$expandableStore",m),w=de();let y=I(e,"collapsed",12,!1),i=I(e,"stickyHeader",8,!1),r=I(e,"expandable",12,!0),x=I(e,"isHeaderStuck",12,!1),u=I(e,"toggleHeader",8,!1),n=I(e,"stickyHeaderTop",24,()=>-.5);const v=ce(y()),p=Te(v,f=>f),$=ce(r());let Q,R=de(!1);function ee(f){r()?v.set(f):v.set(!0)}const D=function(){ee(!d())};He(ye,{collapsed:p,setCollapsed:ee,toggle:D,expandable:$}),F(()=>g(),()=>{r(g())}),F(()=>M(r()),()=>{$.set(r())}),F(()=>d(),()=>{y(d())}),F(()=>M(y()),()=>{v.set(y())}),F(()=>M(r()),()=>{r()||v.set(!0)}),F(()=>M(y()),()=>{y()?(clearTimeout(Q),Q=setTimeout(()=>{q(R,!1)},200)):(clearTimeout(Q),q(R,!0))}),F(()=>(X(w),M(l)),()=>{q(w,l.class)}),Be(),fe();var P=Ve();let se,te;var K=h(P);let oe;var H=h(K);let ne;var $e=h(H);O($e,e,"header",{},null),Ne(K,(f,b)=>function(k,_){const{onStuck:C,onUnstuck:A,offset:T=0}=_,a=document.createElement("div");a.style.position="absolute",a.style.top=T?`${T}px`:"0",a.style.height="1px",a.style.width="100%",a.style.pointerEvents="none",a.style.opacity="0",a.style.zIndex="-1";const L=k.parentNode;if(!L)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(L).position==="static"&&(L.style.position="relative"),L.insertBefore(a,k);const N=new IntersectionObserver(([S])=>{S.isIntersecting?A==null||A():C==null||C()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return N.observe(a),{update(S){_.onStuck=S.onStuck,_.onUnstuck=S.onUnstuck,S.offset!==void 0&&S.offset!==T&&(a.style.top=`${S.offset}px`)},destroy(){N.disconnect(),a.remove()}}}(f,b),()=>({offset:-n(),onStuck:()=>{x(!0)},onUnstuck:()=>{x(!1)}}));var ae=ue(K,2);let le;var be=h(ae),we=h(be),xe=f=>{var b=Je(),k=ge(b);let _;var C=h(k);O(C,e,"default",{},null);var A=ue(k,2),T=a=>{var L=qe(),N=h(L);O(N,e,"footer",{},null),z(a,L)};J(A,a=>{De(()=>o.footer)&&a(T)}),ie(a=>_=B(k,1,"c-collapsible__body svelte-zpen55",null,_,a),[()=>({"c-collapsible__body--should-hide":!X(R)})],re),z(f,b)};J(we,f=>{g()&&f(xe)}),ie((f,b,k,_,C)=>{se=B(P,1,`c-collapsible ${X(w)??""}`,"svelte-zpen55",se,f),te=Me(P,"",te,b),oe=B(K,1,"c-collapsible__header svelte-zpen55",null,oe,k),ne=B(H,1,"c-collapsible__header-inner svelte-zpen55",null,ne,_),Y(H,"role",u()?"button":void 0),Y(H,"aria-expanded",u()?!d():void 0),Y(H,"aria-controls",u()?"collapsible-content":void 0),le=B(ae,1,"c-collapsible__content svelte-zpen55",null,le,C)},[()=>({"is-collapsed":d(),"is-expandable":g()}),()=>({"--sticky-header-top":`${n()}px`}),()=>({"is-sticky":i()}),()=>({"is-collapsed":d(),"is-header-stuck":x(),"has-header-padding":n()>0}),()=>({"is-collapsed":d()})],re),Ue("click",H,function(...f){var b;(b=u()?D:void 0)==null||b.apply(this,f)}),z(s,P),Ge(e,"toggle",D);var ke=me({toggle:D});return c(),ke}var We=W("<svg><!></svg>");function Ze(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=We();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg",width:"8",height:"10","data-ds-icon":"fa",viewBox:"0 0 8 10",...o}));var l=h(t);j(l,()=>'<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',!0),z(s,t)}var je=G('<span class="c-collapse-button-augment__icon svelte-hw7s17"><!></span>');function is(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]),t=E(o,[]);pe(e,!1);const[l,m]=he(),c=()=>V(d,"$collapsed",l),{collapsed:d,setCollapsed:g}=Ye();fe(),Oe(s,Pe({variant:"ghost-block",color:"neutral",size:1},()=>t,{$$events:{click:function(){g(!c())}},children:(w,y)=>{var i=je(),r=h(i);O(r,e,"default",{get collapsed(){return c()}},x=>{var u=Ke(),n=ge(u),v=$=>{Xe($,{})},p=$=>{Ze($,{})};J(n,$=>{c()?$(v):$(p,!1)}),z(x,u)}),z(w,i)},$$slots:{default:!0}})),me(),m()}export{Xe as A,cs as C,ls as T,is as a,Ze as b,Ye as g,ns as k,as as t};
